import { promises as fs } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { AgentConfig, AgentConfigSchema } from '../../types';

export class ConfigManager {
  private configDir: string;
  private configFile: string;

  constructor() {
    this.configDir = join(homedir(), '.agent-cli');
    this.configFile = join(this.configDir, 'config.json');
  }

  /**
   * Load configuration from file
   */
  async loadConfig(): Promise<AgentConfig> {
    try {
      await this.ensureConfigDir();
      const configData = await fs.readFile(this.configFile, 'utf8');
      const config = JSON.parse(configData);
      
      // Validate configuration
      return AgentConfigSchema.parse(config);
    } catch (error) {
      throw new Error(`Failed to load configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Save configuration to file
   */
  async saveConfig(config: AgentConfig): Promise<void> {
    try {
      await this.ensureConfigDir();
      
      // Validate configuration before saving
      const validatedConfig = AgentConfigSchema.parse(config);
      
      await fs.writeFile(
        this.configFile, 
        JSON.stringify(validatedConfig, null, 2), 
        'utf8'
      );
    } catch (error) {
      throw new Error(`Failed to save configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Check if configuration exists
   */
  async configExists(): Promise<boolean> {
    try {
      await fs.access(this.configFile);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Reset configuration (delete config file)
   */
  async resetConfig(): Promise<void> {
    try {
      await fs.unlink(this.configFile);
    } catch (error) {
      // Ignore error if file doesn't exist
      if ((error as any).code !== 'ENOENT') {
        throw new Error(`Failed to reset configuration: ${(error as Error).message}`);
      }
    }
  }

  /**
   * Get configuration file path
   */
  getConfigPath(): string {
    return this.configFile;
  }

  /**
   * Ensure configuration directory exists
   */
  private async ensureConfigDir(): Promise<void> {
    try {
      await fs.mkdir(this.configDir, { recursive: true });
    } catch (error) {
      throw new Error(`Failed to create config directory: ${(error as Error).message}`);
    }
  }

  /**
   * Update specific configuration values
   */
  async updateConfig(updates: Partial<AgentConfig>): Promise<AgentConfig> {
    const currentConfig = await this.loadConfig();
    const newConfig = { ...currentConfig, ...updates };
    await this.saveConfig(newConfig);
    return newConfig;
  }

  /**
   * Get default configuration for a provider
   */
  getDefaultConfig(provider: string): Partial<AgentConfig> {
    const defaults: Record<string, Partial<AgentConfig>> = {
      openai: {
        provider: 'openai',
        model: 'gpt-4-turbo-preview',
        temperature: 0.7,
        maxTokens: 4000
      },
      deepseek: {
        provider: 'deepseek',
        baseUrl: 'https://api.deepseek.com/v1',
        model: 'deepseek-reasoner',
        temperature: 0.7,
        maxTokens: 4000
      },
      ollama: {
        provider: 'ollama',
        baseUrl: 'http://localhost:11434/v1',
        model: 'llama2',
        apiKey: 'ollama',
        temperature: 0.7,
        maxTokens: 4000
      }
    };

    return defaults[provider] || defaults.openai;
  }
}
