import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import { 
  AgentConfig, 
  AgentContext, 
  ConversationMessage, 
  ToolCall, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AgentError,
  AgentEvent
} from '../types';
import { AIProvider } from './ai/AIProvider';
import { ToolRegistry } from './tools/ToolRegistry';
import { ContextManager } from './context/ContextManager';
import { DiffTracker } from './diff/DiffTracker';
import { SemanticSearchEngine } from './semantic/SemanticSearchEngine';
import { GitManager } from './git/GitManager';

/**
 * Central orchestrator that manages the entire agent workflow
 * Coordinates all components and maintains conversation state
 */
export class AgentEngine extends EventEmitter {
  private config: AgentConfig;
  private aiProvider!: AIProvider;
  private toolRegistry!: ToolRegistry;
  private contextManager!: ContextManager;
  private diffTracker!: DiffTracker;
  private semanticSearch!: SemanticSearchEngine;
  private gitManager!: GitManager;
  private context!: AgentContext;
  private isProcessing: boolean = false;

  constructor(config: AgentConfig, workingDirectory: string) {
    super();
    this.config = config;
    this.initializeComponents(workingDirectory).catch(error => {
      this.emit('error', new AgentError(
        `Failed to initialize agent: ${error.message}`,
        'initialization-failed',
        'system',
        false,
        { originalError: error }
      ));
    });
  }

  private async initializeComponents(workingDirectory: string): Promise<void> {
    // Initialize core components
    this.aiProvider = new AIProvider(this.config);
    this.toolRegistry = new ToolRegistry();
    this.contextManager = new ContextManager(workingDirectory);
    this.diffTracker = new DiffTracker();
    this.semanticSearch = new SemanticSearchEngine(workingDirectory);
    this.gitManager = new GitManager(workingDirectory);

    // Initialize context
    this.context = await this.contextManager.initializeContext();

    // Register event handlers
    this.setupEventHandlers();

    // Register core tools
    await this.registerCoreTools();

    this.emit('initialized', { context: this.context });
  }

  private setupEventHandlers(): void {
    // Tool execution events
    this.toolRegistry.on('tool-executed', (result: ToolResult) => {
      this.handleToolResult(result);
    });

    // Diff tracking events
    this.diffTracker.on('change-detected', (change) => {
      this.context.changeHistory.push(change);
      this.emit('change-tracked', change);
    });

    // Git events
    this.gitManager.on('git-operation', (operation) => {
      this.emit('git-operation', operation);
    });

    // Error handling
    this.on('error', (error: AgentError) => {
      this.handleError(error);
    });
  }

  private async registerCoreTools(): Promise<void> {
    // Import and register file operations tools
    const fileOpsModule = await import('../tools/FileOperations');
    const fileOpsTools = fileOpsModule.default;
    for (const tool of fileOpsTools) {
      await this.toolRegistry.registerTool(tool);
    }

    // Import and register shell command tools
    const shellModule = await import('../tools/ShellCommands');
    const shellTools = shellModule.default;
    for (const tool of shellTools) {
      await this.toolRegistry.registerTool(tool);
    }

    // Import and register git operation tools
    const gitModule = await import('../tools/GitOperations');
    const gitTools = gitModule.default;
    for (const tool of gitTools) {
      await this.toolRegistry.registerTool(tool);
    }

    // Import and register semantic search tools
    const semanticModule = await import('../tools/SemanticSearch');
    const semanticTools = semanticModule.default;
    for (const tool of semanticTools) {
      await this.toolRegistry.registerTool(tool);
    }

    // Import and register context management tools
    const contextModule = await import('../tools/ContextManagement');
    const contextTools = contextModule.default;
    for (const tool of contextTools) {
      await this.toolRegistry.registerTool(tool);
    }

    // Import and register debugging tools
    const debuggingModule = await import('../tools/DebuggingTools');
    const debuggingTools = debuggingModule.default;
    for (const tool of debuggingTools) {
      await this.toolRegistry.registerTool(tool);
    }
  }

  /**
   * Process user input and execute autonomous workflow
   */
  async processInput(input: string): Promise<void> {
    if (this.isProcessing) {
      throw new AgentError('Agent is already processing a request', 'agent-busy', 'user-input', false);
    }

    this.isProcessing = true;
    
    try {
      // Create user message
      const userMessage: ConversationMessage = {
        id: nanoid(),
        role: 'user',
        content: input,
        timestamp: new Date()
      };

      // Add to conversation history
      this.context.conversationHistory.push(userMessage);

      // Emit processing start event
      this.emit('processing-started', { input, context: this.context });

      // Start autonomous execution loop
      await this.autonomousExecutionLoop();

    } catch (error) {
      this.handleError(error as AgentError);
    } finally {
      this.isProcessing = false;
      this.emit('processing-completed');
    }
  }

  /**
   * Autonomous execution loop - continues until task completion
   */
  private async autonomousExecutionLoop(): Promise<void> {
    let maxIterations = 50; // Prevent infinite loops
    let iteration = 0;

    while (iteration < maxIterations) {
      iteration++;

      try {
        // Get AI response with tool calls
        const response = await this.aiProvider.getResponse(
          this.context.conversationHistory,
          this.toolRegistry.getToolSchemas(),
          this.context
        );

        // Add AI response to conversation
        const assistantMessage: ConversationMessage = {
          id: nanoid(),
          role: 'assistant',
          content: response.content,
          timestamp: new Date(),
          toolCalls: response.toolCalls
        };

        this.context.conversationHistory.push(assistantMessage);

        // Execute tool calls if present
        if (response.toolCalls && response.toolCalls.length > 0) {
          const toolResults = await this.executeToolCalls(response.toolCalls);
          assistantMessage.toolResults = toolResults;

          // Check if more iterations are needed
          const needsContinuation = this.shouldContinueExecution(toolResults);
          if (!needsContinuation) {
            break;
          }
        } else {
          // No tool calls, task is complete
          break;
        }

        // Update context after each iteration
        await this.contextManager.updateContext(this.context);

      } catch (error) {
        this.handleError(error as AgentError);
        break;
      }
    }

    if (iteration >= maxIterations) {
      this.emit('max-iterations-reached', { iterations: maxIterations });
    }
  }

  /**
   * Execute multiple tool calls in sequence or parallel
   */
  private async executeToolCalls(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];

    for (const toolCall of toolCalls) {
      try {
        this.emit('tool-execution-started', toolCall);

        const result = await this.toolRegistry.executeTool(
          toolCall.name,
          toolCall.parameters,
          this.context
        );

        results.push(result);
        this.emit('tool-execution-completed', result);

      } catch (error) {
        const errorResult: ToolResult = {
          id: nanoid(),
          toolCallId: toolCall.id,
          success: false,
          output: null,
          error: (error as Error).message,
          timestamp: new Date()
        };

        results.push(errorResult);
        this.emit('tool-execution-failed', errorResult);
      }
    }

    return results;
  }

  /**
   * Determine if execution should continue based on tool results
   */
  private shouldContinueExecution(toolResults: ToolResult[]): boolean {
    // Continue if any tool indicates more work is needed
    return toolResults.some(result => 
      result.metadata?.continueExecution === true ||
      result.metadata?.requiresFollowup === true
    );
  }

  /**
   * Handle tool execution results
   */
  private handleToolResult(result: ToolResult): void {
    // Update context based on tool result
    if (result.success && result.metadata?.contextUpdate) {
      Object.assign(this.context, result.metadata.contextUpdate);
    }

    // Track changes if file operations occurred
    if (result.metadata?.fileChanges) {
      this.diffTracker.trackChanges(result.metadata.fileChanges);
    }

    this.emit('tool-result-processed', result);
  }

  /**
   * Handle errors with recovery strategies
   */
  private handleError(error: AgentError): void {
    console.error('Agent Error:', error);

    // Implement recovery strategies based on error type
    if (error.recoverable) {
      this.emit('error-recovery-attempted', error);
      // Implement specific recovery logic
    } else {
      this.emit('fatal-error', error);
    }
  }

  /**
   * Get current agent context
   */
  getContext(): AgentContext {
    return { ...this.context };
  }

  /**
   * Update agent configuration
   */
  async updateConfig(newConfig: Partial<AgentConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    this.aiProvider.updateConfig(this.config);
    this.emit('config-updated', this.config);
  }

  /**
   * Reset agent state
   */
  async reset(): Promise<void> {
    this.context = await this.contextManager.initializeContext();
    this.isProcessing = false;
    this.emit('reset-completed');
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    this.isProcessing = false;
    await this.contextManager.saveContext(this.context);
    this.emit('shutdown-completed');
  }
}
