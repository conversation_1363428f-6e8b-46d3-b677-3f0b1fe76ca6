import { promises as fs } from 'fs';
import { join } from 'path';
import { nanoid } from 'nanoid';
import { AgentContext, GitRepository, ConversationMessage, ChangeRecord } from '../../types';
import { GitManager } from '../git/GitManager';

export class ContextManager {
  private workingDirectory: string;
  private contextFile: string;
  private gitManager: GitManager;

  constructor(workingDirectory: string) {
    this.workingDirectory = workingDirectory;
    this.contextFile = join(workingDirectory, '.agent-context.json');
    this.gitManager = new GitManager(workingDirectory);
  }

  /**
   * Initialize a new agent context
   */
  async initializeContext(): Promise<AgentContext> {
    const sessionId = nanoid();
    
    // Check if we're in a git repository
    let gitRepository: GitRepository | undefined;
    try {
      gitRepository = await this.gitManager.getRepositoryInfo();
    } catch {
      // Not a git repository, that's okay
    }

    const context: AgentContext = {
      sessionId,
      workingDirectory: this.workingDirectory,
      gitRepository,
      conversationHistory: [],
      activeTools: [
        'read_file',
        'write_file',
        'delete_file',
        'list_directory',
        'search_files',
        'execute_command',
        'interactive_command',
        'get_system_info',
        'check_process',
        'git_add',
        'git_commit',
        'git_status',
        'git_log',
        'git_branch',
        'git_diff',
        'analyze_error',
        'run_tests',
        'check_syntax',
        'generate_fix',
        'monitor_process',
        'get_context',
        'clear_history',
        'get_session_stats',
        'export_context',
        'index_file',
        'semantic_search'
      ],
      changeHistory: [],
      semanticIndex: undefined
    };

    // Try to load existing context
    try {
      const existingContext = await this.loadContext();
      // Merge with existing context but keep new session ID
      context.conversationHistory = existingContext.conversationHistory || [];
      context.changeHistory = existingContext.changeHistory || [];
      context.semanticIndex = existingContext.semanticIndex;
    } catch {
      // No existing context, start fresh
    }

    return context;
  }

  /**
   * Load context from file
   */
  async loadContext(): Promise<Partial<AgentContext>> {
    try {
      const contextData = await fs.readFile(this.contextFile, 'utf8');
      const context = JSON.parse(contextData);
      
      // Convert date strings back to Date objects
      if (context.conversationHistory) {
        context.conversationHistory = context.conversationHistory.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
      }

      if (context.changeHistory) {
        context.changeHistory = context.changeHistory.map((change: any) => ({
          ...change,
          timestamp: new Date(change.timestamp)
        }));
      }

      return context;
    } catch (error) {
      throw new Error(`Failed to load context: ${(error as Error).message}`);
    }
  }

  /**
   * Save context to file
   */
  async saveContext(context: AgentContext): Promise<void> {
    try {
      // Create a serializable version of the context
      const serializableContext = {
        sessionId: context.sessionId,
        workingDirectory: context.workingDirectory,
        gitRepository: context.gitRepository,
        conversationHistory: context.conversationHistory.slice(-50), // Keep last 50 messages
        activeTools: context.activeTools,
        changeHistory: context.changeHistory.slice(-100), // Keep last 100 changes
        semanticIndex: context.semanticIndex ? {
          lastUpdated: context.semanticIndex.lastUpdated,
          version: context.semanticIndex.version
          // Don't save embeddings to file, they're too large
        } : undefined,
        lastSaved: new Date()
      };

      await fs.writeFile(
        this.contextFile,
        JSON.stringify(serializableContext, null, 2),
        'utf8'
      );
    } catch (error) {
      throw new Error(`Failed to save context: ${(error as Error).message}`);
    }
  }

  /**
   * Update context with new information
   */
  async updateContext(context: AgentContext): Promise<void> {
    // Update git repository info if available
    try {
      context.gitRepository = await this.gitManager.getRepositoryInfo();
    } catch {
      // Git info not available
    }

    // Auto-save context periodically
    await this.saveContext(context);
  }

  /**
   * Add conversation message to context
   */
  addConversationMessage(context: AgentContext, message: ConversationMessage): void {
    context.conversationHistory.push(message);
    
    // Keep conversation history manageable
    if (context.conversationHistory.length > 100) {
      context.conversationHistory = context.conversationHistory.slice(-50);
    }
  }

  /**
   * Add change record to context
   */
  addChangeRecord(context: AgentContext, change: ChangeRecord): void {
    context.changeHistory.push(change);
    
    // Keep change history manageable
    if (context.changeHistory.length > 200) {
      context.changeHistory = context.changeHistory.slice(-100);
    }
  }

  /**
   * Get recent changes
   */
  getRecentChanges(context: AgentContext, limit: number = 10): ChangeRecord[] {
    return context.changeHistory.slice(-limit);
  }

  /**
   * Get conversation summary
   */
  getConversationSummary(context: AgentContext): string {
    const messageCount = context.conversationHistory.length;
    const userMessages = context.conversationHistory.filter(m => m.role === 'user').length;
    const assistantMessages = context.conversationHistory.filter(m => m.role === 'assistant').length;
    
    return `${messageCount} total messages (${userMessages} user, ${assistantMessages} assistant)`;
  }

  /**
   * Clear conversation history
   */
  clearConversationHistory(context: AgentContext): void {
    context.conversationHistory = [];
  }

  /**
   * Clear change history
   */
  clearChangeHistory(context: AgentContext): void {
    context.changeHistory = [];
  }

  /**
   * Get context statistics
   */
  getContextStats(context: AgentContext): Record<string, any> {
    const now = new Date();
    const recentChanges = context.changeHistory.filter(
      change => now.getTime() - change.timestamp.getTime() < 3600000 // Last hour
    );

    return {
      sessionId: context.sessionId,
      workingDirectory: context.workingDirectory,
      hasGitRepo: !!context.gitRepository,
      conversationMessages: context.conversationHistory.length,
      totalChanges: context.changeHistory.length,
      recentChanges: recentChanges.length,
      activeTools: context.activeTools.length,
      hasSemanticIndex: !!context.semanticIndex
    };
  }

  /**
   * Export context for backup
   */
  async exportContext(context: AgentContext, filePath: string): Promise<void> {
    try {
      const exportData = {
        ...context,
        exportedAt: new Date(),
        version: '1.0.0'
      };

      await fs.writeFile(filePath, JSON.stringify(exportData, null, 2), 'utf8');
    } catch (error) {
      throw new Error(`Failed to export context: ${(error as Error).message}`);
    }
  }

  /**
   * Import context from backup
   */
  async importContext(filePath: string): Promise<Partial<AgentContext>> {
    try {
      const contextData = await fs.readFile(filePath, 'utf8');
      const context = JSON.parse(contextData);
      
      // Validate and convert dates
      if (context.conversationHistory) {
        context.conversationHistory = context.conversationHistory.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
      }

      if (context.changeHistory) {
        context.changeHistory = context.changeHistory.map((change: any) => ({
          ...change,
          timestamp: new Date(change.timestamp)
        }));
      }

      return context;
    } catch (error) {
      throw new Error(`Failed to import context: ${(error as Error).message}`);
    }
  }

  /**
   * Check if context file exists
   */
  async contextExists(): Promise<boolean> {
    try {
      await fs.access(this.contextFile);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Delete context file
   */
  async deleteContext(): Promise<void> {
    try {
      await fs.unlink(this.contextFile);
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        throw new Error(`Failed to delete context: ${(error as Error).message}`);
      }
    }
  }
}
