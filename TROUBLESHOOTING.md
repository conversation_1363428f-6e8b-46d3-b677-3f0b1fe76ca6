# Autonomous Agent CLI - Troubleshooting Guide

## 🔧 Common Issues and Solutions

### Configuration Issues

#### Issue: "Invalid url" validation error
**Symptoms:**
```
Error starting agent: Failed to save configuration: [
  {
    "validation": "url",
    "code": "invalid_string",
    "message": "Invalid url",
    "path": ["baseUrl"]
  }
]
```

**Solution:**
This has been fixed in the latest version. If you encounter this:
1. Update to the latest version
2. Reset your configuration: `npx tsx src/cli.ts config --reset`
3. Reconfigure: `npx tsx src/cli.ts config`

#### Issue: API Key Problems
**Symptoms:**
- "Invalid API key" errors
- "Unauthorized access" errors

**Solutions:**
1. **Verify API Key Format:**
   - OpenAI: Should start with `sk-`
   - Deepseek: Should start with `sk-`
   - Ollama: Use `ollama` (any value works)

2. **Test Configuration:**
   ```bash
   npx tsx src/cli.ts config --test
   ```

3. **Reset and Reconfigure:**
   ```bash
   npx tsx src/cli.ts config --reset
   npx tsx src/cli.ts config
   ```

#### Issue: Model Not Found
**Symptoms:**
- "Model 'xyz' not found" errors

**Solutions:**
1. **Check Available Models:**
   - OpenAI: `gpt-4-turbo-preview`, `gpt-4`, `gpt-3.5-turbo`
   - Deepseek: `deepseek-reasoner`, `deepseek-chat`
   - Ollama: Run `ollama list` to see installed models

2. **Update Model Name:**
   ```bash
   npx tsx src/cli.ts config
   ```

### Provider-Specific Issues

#### OpenAI
**Common Issues:**
- Rate limiting
- Quota exceeded
- Invalid API key

**Solutions:**
1. Check your OpenAI account usage and limits
2. Verify API key is active and has sufficient credits
3. Use `--temperature 0.5` to reduce token usage

#### Deepseek
**Common Issues:**
- Base URL configuration
- Model availability

**Solutions:**
1. Ensure base URL is set to `https://api.deepseek.com/v1`
2. Use model `deepseek-reasoner` or `deepseek-chat`
3. Verify your Deepseek account is active

#### Ollama (Local)
**Common Issues:**
- Ollama not running
- Model not installed
- Connection refused

**Solutions:**
1. **Start Ollama:**
   ```bash
   ollama serve
   ```

2. **Install a Model:**
   ```bash
   ollama pull llama2
   # or
   ollama pull codellama
   ```

3. **Check Ollama Status:**
   ```bash
   curl http://localhost:11434/api/tags
   ```

### Runtime Issues

#### Issue: Agent Hangs or Stops Responding
**Solutions:**
1. **Check Process:**
   - Press `Ctrl+C` to stop
   - Restart the agent

2. **Check Configuration:**
   ```bash
   npx tsx src/cli.ts config --test
   ```

3. **Clear Context:**
   - Use `clear` command in interactive mode
   - Or restart the agent

#### Issue: Tool Execution Failures
**Solutions:**
1. **Check Permissions:**
   - Ensure the agent has file system permissions
   - Run from a directory you own

2. **Check Working Directory:**
   - Ensure you're in the correct project directory
   - Use `status` command to verify context

3. **Review Recent Changes:**
   - Use `changes` command to see what was modified
   - Use `diff <id>` to see specific changes

### Performance Issues

#### Issue: Slow Response Times
**Solutions:**
1. **Reduce Token Usage:**
   - Lower `maxTokens` setting
   - Use simpler prompts

2. **Check Network:**
   - Test internet connection
   - Try different provider if available

3. **Optimize Context:**
   - Clear conversation history periodically
   - Use `clear` command in interactive mode

## 🛠 Diagnostic Commands

### Configuration Diagnostics
```bash
# Show current configuration
npx tsx src/cli.ts config --show

# Test configuration
npx tsx src/cli.ts config --test

# Reset configuration
npx tsx src/cli.ts config --reset
```

### Runtime Diagnostics
```bash
# Start interactive mode with verbose output
npx tsx src/cli.ts chat

# In interactive mode:
status          # Show agent status
changes         # Show recent changes
diff <id>       # Show specific change details
help            # Show available commands
```

### System Diagnostics
```bash
# Check Node.js version (requires 18+)
node --version

# Check npm packages
npm list

# Rebuild project
npm run build

# Check for TypeScript errors
npx tsc --noEmit
```

## 🔍 Debug Mode

### Enable Debug Logging
Set environment variable:
```bash
# Windows
set DEBUG=true
npx tsx src/cli.ts chat

# Linux/Mac
DEBUG=true npx tsx src/cli.ts chat
```

### Verbose Output
For more detailed output, check the agent status frequently:
```
🤖 > status
📊 Agent Status:
  Session ID: abc123
  Working Directory: /path/to/project
  Git Repository: Yes
  Active Tools: 26
  Conversation History: 5 messages
  Changes Tracked: 3
  Processing: No
```

## 🆘 Getting Help

### Built-in Help
```bash
# CLI help
npx tsx src/cli.ts --help

# Interactive help
🤖 > help
```

### Log Files
Check for log files in:
- `~/.agent-cli/` (configuration directory)
- Current working directory

### Common Error Patterns

#### Network Errors
```
Connection failed to [provider]. Please check your internet connection and base URL.
```
**Solution:** Check internet connection and provider status

#### Authentication Errors
```
Invalid API key for [provider]. Please check your API key configuration.
```
**Solution:** Verify API key is correct and active

#### Model Errors
```
Model '[model]' not found for [provider]. Please check the model name.
```
**Solution:** Use correct model name for the provider

#### Rate Limit Errors
```
Rate limit exceeded for [provider]. Please wait and try again.
```
**Solution:** Wait and retry, or upgrade your plan

## 📋 Checklist for Issues

Before reporting issues, please check:

- [ ] Node.js version 18 or higher
- [ ] Latest version of the agent
- [ ] Configuration is valid (`config --test`)
- [ ] API key is correct and active
- [ ] Internet connection is working
- [ ] Provider service is available
- [ ] Sufficient permissions in working directory
- [ ] No antivirus blocking the application

## 🔄 Reset Everything

If all else fails, complete reset:
```bash
# Reset configuration
npx tsx src/cli.ts config --reset

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Rebuild
npm run build

# Reconfigure
npx tsx src/cli.ts config
```

---

For additional help, refer to:
- [README.md](README.md) - Main documentation
- [USAGE.md](USAGE.md) - Usage examples
- GitHub Issues - Report bugs and get help
