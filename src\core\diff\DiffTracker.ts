import { EventEmitter } from 'events';
import { createPatch, parsePatch, diffLines } from 'diff';
import { ChangeRecord, DiffResult, DiffHunk, DiffLine, DiffStats } from '../../types';
import { nanoid } from 'nanoid';

export class DiffTracker extends EventEmitter {
  private changes: Map<string, ChangeRecord> = new Map();

  /**
   * Track changes to files
   */
  trackChanges(changes: ChangeRecord[]): void {
    for (const change of changes) {
      this.trackChange(change);
    }
  }

  /**
   * Track a single change
   */
  trackChange(change: ChangeRecord): void {
    // Generate diff if not already present
    if (change.diff && change.diff.oldContent && change.diff.newContent) {
      change.diff = this.generateDiff(
        change.diff.oldContent,
        change.diff.newContent,
        change.path
      );
    }

    this.changes.set(change.id, change);
    this.emit('change-detected', change);
  }

  /**
   * Generate diff between old and new content
   */
  generateDiff(oldContent: string, newContent: string, filePath: string): DiffResult {
    // Create unified diff patch
    const patch = createPatch(filePath, oldContent, newContent);
    
    // Parse the patch to extract hunks
    const parsedPatch = parsePatch(patch)[0];
    
    const hunks: DiffHunk[] = parsedPatch.hunks.map(hunk => ({
      oldStart: hunk.oldStart,
      oldLines: hunk.oldLines,
      newStart: hunk.newStart,
      newLines: hunk.newLines,
      lines: hunk.lines.map(line => ({
        type: line.startsWith('+') ? 'add' : line.startsWith('-') ? 'remove' : 'context',
        content: line.substring(1), // Remove the +/- prefix
        lineNumber: undefined // Will be calculated if needed
      }))
    }));

    // Calculate statistics
    const stats = this.calculateDiffStats(hunks);

    return {
      oldContent,
      newContent,
      hunks,
      stats
    };
  }

  /**
   * Calculate diff statistics
   */
  private calculateDiffStats(hunks: DiffHunk[]): DiffStats {
    let additions = 0;
    let deletions = 0;
    let changes = 0;

    for (const hunk of hunks) {
      for (const line of hunk.lines) {
        if (line.type === 'add') {
          additions++;
        } else if (line.type === 'remove') {
          deletions++;
        }
      }
    }

    // Changes are pairs of additions and deletions
    changes = Math.min(additions, deletions);

    return {
      additions,
      deletions,
      changes
    };
  }

  /**
   * Get all tracked changes
   */
  getAllChanges(): ChangeRecord[] {
    return Array.from(this.changes.values());
  }

  /**
   * Get changes for a specific file
   */
  getChangesForFile(filePath: string): ChangeRecord[] {
    return Array.from(this.changes.values()).filter(
      change => change.path === filePath
    );
  }

  /**
   * Get recent changes
   */
  getRecentChanges(limit: number = 10): ChangeRecord[] {
    const allChanges = Array.from(this.changes.values());
    return allChanges
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Clear all tracked changes
   */
  clearChanges(): void {
    this.changes.clear();
    this.emit('changes-cleared');
  }

  /**
   * Remove specific change
   */
  removeChange(changeId: string): boolean {
    const removed = this.changes.delete(changeId);
    if (removed) {
      this.emit('change-removed', changeId);
    }
    return removed;
  }

  /**
   * Get change by ID
   */
  getChange(changeId: string): ChangeRecord | undefined {
    return this.changes.get(changeId);
  }

  /**
   * Format diff for terminal display
   */
  formatDiffForTerminal(diff: DiffResult, maxLines: number = 50): string {
    const lines: string[] = [];
    let lineCount = 0;

    for (const hunk of diff.hunks) {
      if (lineCount >= maxLines) {
        lines.push('... (truncated)');
        break;
      }

      // Add hunk header
      lines.push(`@@ -${hunk.oldStart},${hunk.oldLines} +${hunk.newStart},${hunk.newLines} @@`);
      lineCount++;

      for (const line of hunk.lines) {
        if (lineCount >= maxLines) {
          lines.push('... (truncated)');
          break;
        }

        let prefix = ' ';
        if (line.type === 'add') {
          prefix = '+';
        } else if (line.type === 'remove') {
          prefix = '-';
        }

        lines.push(`${prefix}${line.content}`);
        lineCount++;
      }
    }

    return lines.join('\n');
  }

  /**
   * Get diff summary
   */
  getDiffSummary(diff: DiffResult): string {
    const { additions, deletions, changes } = diff.stats;
    const parts: string[] = [];

    if (additions > 0) {
      parts.push(`+${additions}`);
    }
    if (deletions > 0) {
      parts.push(`-${deletions}`);
    }
    if (changes > 0) {
      parts.push(`~${changes}`);
    }

    return parts.join(' ');
  }

  /**
   * Compare two files and generate diff
   */
  async compareFiles(oldContent: string, newContent: string, filePath: string): Promise<DiffResult> {
    return this.generateDiff(oldContent, newContent, filePath);
  }

  /**
   * Get changes by type
   */
  getChangesByType(type: ChangeRecord['type']): ChangeRecord[] {
    return Array.from(this.changes.values()).filter(
      change => change.type === type
    );
  }

  /**
   * Get changes in time range
   */
  getChangesInTimeRange(startTime: Date, endTime: Date): ChangeRecord[] {
    return Array.from(this.changes.values()).filter(
      change => change.timestamp >= startTime && change.timestamp <= endTime
    );
  }

  /**
   * Export changes to JSON
   */
  exportChanges(): string {
    const changes = Array.from(this.changes.values());
    return JSON.stringify(changes, null, 2);
  }

  /**
   * Import changes from JSON
   */
  importChanges(changesJson: string): void {
    try {
      const changes: ChangeRecord[] = JSON.parse(changesJson);
      
      for (const change of changes) {
        // Convert timestamp string back to Date
        change.timestamp = new Date(change.timestamp);
        this.changes.set(change.id, change);
      }

      this.emit('changes-imported', changes.length);
    } catch (error) {
      throw new Error(`Failed to import changes: ${(error as Error).message}`);
    }
  }

  /**
   * Get statistics about tracked changes
   */
  getChangeStats(): Record<string, any> {
    const changes = Array.from(this.changes.values());
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 3600000);
    const oneDayAgo = new Date(now.getTime() - 86400000);

    const recentChanges = changes.filter(c => c.timestamp >= oneHourAgo);
    const todayChanges = changes.filter(c => c.timestamp >= oneDayAgo);

    const typeStats = changes.reduce((acc, change) => {
      acc[change.type] = (acc[change.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalChanges: changes.length,
      recentChanges: recentChanges.length,
      todayChanges: todayChanges.length,
      typeBreakdown: typeStats,
      oldestChange: changes.length > 0 
        ? Math.min(...changes.map(c => c.timestamp.getTime()))
        : null,
      newestChange: changes.length > 0 
        ? Math.max(...changes.map(c => c.timestamp.getTime()))
        : null
    };
  }
}
