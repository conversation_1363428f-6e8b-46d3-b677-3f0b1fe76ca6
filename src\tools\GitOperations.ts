import { z } from 'zod';
import { nanoid } from 'nanoid';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ult, AgentContext, ChangeRecord } from '../types';
import { GitManager } from '../core/git/GitManager';

// Git Status Tool
const gitStatusSchema = z.object({
  detailed: z.boolean().default(false).optional().describe('Include detailed status information')
});

const gitStatusTool: Tool = {
  name: 'git_status',
  description: 'Get the current git repository status',
  schema: gitStatusSchema,
  category: 'git-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const gitManager = new GitManager(context.workingDirectory);
      
      if (!await gitManager.isRepository()) {
        return {
          id: nanoid(),
          toolCallId: nanoid(),
          success: false,
          output: null,
          error: 'Not a git repository',
          timestamp: new Date()
        };
      }

      const status = await gitManager.getStatus();
      const repoInfo = params.detailed ? await gitManager.getRepositoryInfo() : null;

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          status,
          ...(params.detailed && { repository: repoInfo })
        },
        timestamp: new Date(),
        metadata: {
          operation: 'status',
          hasChanges: status.modified.length > 0 || status.added.length > 0 || status.deleted.length > 0
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Git status failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Git Add Tool
const gitAddSchema = z.object({
  files: z.array(z.string()).optional().describe('Files to stage (if empty, stages all changes)'),
  all: z.boolean().default(false).optional().describe('Stage all changes including deletions')
});

const gitAddTool: Tool = {
  name: 'git_add',
  description: 'Stage files for commit',
  schema: gitAddSchema,
  category: 'git-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const gitManager = new GitManager(context.workingDirectory);

      if (params.all || !params.files || params.files.length === 0) {
        await gitManager.stageAll();
      } else {
        await gitManager.stageFiles(params.files);
      }

      const changeRecord: ChangeRecord = {
        id: nanoid(),
        type: 'git-operation',
        path: 'git-staging',
        timestamp: new Date(),
        metadata: {
          operation: 'stage',
          files: params.files || ['all']
        }
      };

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          staged: params.files || 'all changes',
          operation: 'stage'
        },
        timestamp: new Date(),
        metadata: {
          operation: 'stage',
          fileChanges: [changeRecord]
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Git add failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Git Commit Tool
const gitCommitSchema = z.object({
  message: z.string().describe('Commit message'),
  addAll: z.boolean().default(false).optional().describe('Stage all changes before committing')
});

const gitCommitTool: Tool = {
  name: 'git_commit',
  description: 'Commit staged changes',
  schema: gitCommitSchema,
  category: 'git-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const gitManager = new GitManager(context.workingDirectory);

      // Stage all changes if requested
      if (params.addAll) {
        await gitManager.stageAll();
      }

      const commit = await gitManager.commit(params.message);

      const changeRecord: ChangeRecord = {
        id: nanoid(),
        type: 'git-operation',
        path: 'git-commit',
        timestamp: new Date(),
        metadata: {
          operation: 'commit',
          commit: commit
        }
      };

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          commit,
          message: params.message
        },
        timestamp: new Date(),
        metadata: {
          operation: 'commit',
          fileChanges: [changeRecord]
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Git commit failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Git Branch Tool
const gitBranchSchema = z.object({
  action: z.enum(['list', 'create', 'switch', 'delete']).describe('Branch action to perform'),
  branchName: z.string().optional().describe('Branch name (required for create, switch, delete)'),
  switchTo: z.boolean().default(true).optional().describe('Switch to branch after creating')
});

const gitBranchTool: Tool = {
  name: 'git_branch',
  description: 'Manage git branches',
  schema: gitBranchSchema,
  category: 'git-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const gitManager = new GitManager(context.workingDirectory);

      let result: any = {};

      switch (params.action) {
        case 'list':
          const branches = await gitManager.getBranches();
          result = { branches, current: context.gitRepository?.currentBranch };
          break;

        case 'create':
          if (!params.branchName) {
            throw new Error('Branch name is required for create action');
          }
          await gitManager.createBranch(params.branchName, params.switchTo);
          result = { created: params.branchName, switchedTo: params.switchTo };
          break;

        case 'switch':
          if (!params.branchName) {
            throw new Error('Branch name is required for switch action');
          }
          await gitManager.switchBranch(params.branchName);
          result = { switchedTo: params.branchName };
          break;

        case 'delete':
          // Note: Delete operation would need additional implementation in GitManager
          throw new Error('Delete branch operation not yet implemented');

        default:
          throw new Error(`Unknown branch action: ${params.action}`);
      }

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          action: params.action,
          ...result
        },
        timestamp: new Date(),
        metadata: {
          operation: `branch-${params.action}`,
          branchName: params.branchName
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Git branch operation failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Git Diff Tool
const gitDiffSchema = z.object({
  files: z.array(z.string()).optional().describe('Specific files to diff'),
  staged: z.boolean().default(false).optional().describe('Show diff of staged changes'),
  commit1: z.string().optional().describe('First commit for comparison'),
  commit2: z.string().optional().describe('Second commit for comparison')
});

const gitDiffTool: Tool = {
  name: 'git_diff',
  description: 'Show differences in git repository',
  schema: gitDiffSchema,
  category: 'git-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const gitManager = new GitManager(context.workingDirectory);
      let diff: string;

      if (params.commit1 && params.commit2) {
        diff = await gitManager.getDiffBetweenCommits(params.commit1, params.commit2);
      } else {
        diff = await gitManager.getDiff(params.files);
      }

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          diff,
          files: params.files,
          staged: params.staged,
          commits: params.commit1 && params.commit2 ? [params.commit1, params.commit2] : undefined
        },
        timestamp: new Date(),
        metadata: {
          operation: 'diff',
          hasChanges: diff.length > 0
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Git diff failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Git Log Tool
const gitLogSchema = z.object({
  limit: z.number().default(10).optional().describe('Number of commits to show'),
  oneline: z.boolean().default(false).optional().describe('Show one line per commit')
});

const gitLogTool: Tool = {
  name: 'git_log',
  description: 'Show git commit history',
  schema: gitLogSchema,
  category: 'git-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const gitManager = new GitManager(context.workingDirectory);
      const commits = await gitManager.getCommitHistory(params.limit);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          commits,
          count: commits.length,
          limit: params.limit
        },
        timestamp: new Date(),
        metadata: {
          operation: 'log',
          commitCount: commits.length
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Git log failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Export all tools as default array
export default [
  gitStatusTool,
  gitAddTool,
  gitCommitTool,
  gitBranchTool,
  gitDiffTool,
  gitLogTool
];
