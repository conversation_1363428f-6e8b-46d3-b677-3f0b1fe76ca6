#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { config } from 'dotenv';
import { AgentEngine } from './core/AgentEngine';
import { AgentConfig, AIProvider } from './types';
import { TerminalUI } from './ui/TerminalUI';
import { ConfigManager } from './core/config/ConfigManager';

// Load environment variables
config();

const program = new Command();

program
  .name('agent')
  .description('Autonomous AI-powered CLI tool')
  .version('1.0.0');

// Interactive mode command
program
  .command('chat')
  .description('Start interactive chat mode')
  .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama)')
  .option('-m, --model <model>', 'Model to use')
  .option('-k, --api-key <key>', 'API key for the provider')
  .option('-u, --base-url <url>', 'Base URL for the provider')
  .option('-t, --temperature <temp>', 'Temperature for AI responses', '0.7')
  .option('-d, --directory <dir>', 'Working directory', process.cwd())
  .action(async (options) => {
    try {
      const configManager = new ConfigManager();
      let agentConfig: AgentConfig;

      // Try to load existing config or create new one
      try {
        agentConfig = await configManager.loadConfig();
        console.log(chalk.green('✓ Loaded existing configuration'));
      } catch {
        console.log(chalk.yellow('No existing configuration found. Setting up...'));
        agentConfig = await setupConfiguration(options);
        await configManager.saveConfig(agentConfig);
      }

      // Override with command line options (only if explicitly provided)
      if (process.argv.includes('-p') || process.argv.includes('--provider')) {
        agentConfig.provider = options.provider as AIProvider;
      }
      if (options.model) agentConfig.model = options.model;
      if (options.apiKey) agentConfig.apiKey = options.apiKey;
      if (options.baseUrl) agentConfig.baseUrl = options.baseUrl;
      if (options.temperature) agentConfig.temperature = parseFloat(options.temperature);

      // Validate configuration
      if (!agentConfig.apiKey && agentConfig.provider !== 'ollama') {
        console.error(chalk.red('Error: API key is required for this provider'));
        process.exit(1);
      }

      // Initialize agent
      const spinner = ora('Initializing agent...').start();
      const agent = new AgentEngine(agentConfig, options.directory);
      
      // Wait for initialization
      await new Promise((resolve) => {
        agent.once('initialized', resolve);
      });
      
      spinner.succeed('Agent initialized successfully');

      // Start terminal UI
      const terminalUI = new TerminalUI(agent);
      await terminalUI.start();

    } catch (error) {
      console.error(chalk.red('Error starting agent:'), (error as Error).message);
      process.exit(1);
    }
  });

// One-shot command execution
program
  .command('run <prompt>')
  .description('Execute a single prompt and exit')
  .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama)')
  .option('-m, --model <model>', 'Model to use')
  .option('-k, --api-key <key>', 'API key for the provider')
  .option('-u, --base-url <url>', 'Base URL for the provider')
  .option('-d, --directory <dir>', 'Working directory', process.cwd())
  .action(async (prompt, options) => {
    try {
      const configManager = new ConfigManager();
      let agentConfig: AgentConfig;

      try {
        agentConfig = await configManager.loadConfig();
      } catch {
        agentConfig = await setupConfiguration(options);
      }

      // Override with command line options (only if explicitly provided)
      if (process.argv.includes('-p') || process.argv.includes('--provider')) {
        agentConfig.provider = options.provider as AIProvider;
      }
      if (options.model) agentConfig.model = options.model;
      if (options.apiKey) agentConfig.apiKey = options.apiKey;
      if (options.baseUrl) agentConfig.baseUrl = options.baseUrl;

      // Initialize and run
      const spinner = ora('Processing request...').start();
      const agent = new AgentEngine(agentConfig, options.directory);
      
      await new Promise((resolve) => {
        agent.once('initialized', resolve);
      });

      // Set up event handlers for output
      agent.on('processing-started', () => {
        spinner.text = 'Agent is working...';
      });

      agent.on('tool-execution-started', (toolCall) => {
        spinner.text = `Executing: ${toolCall.name}`;
      });

      agent.on('processing-completed', () => {
        spinner.succeed('Task completed');
      });

      // Process the prompt
      await agent.processInput(prompt);

    } catch (error) {
      console.error(chalk.red('Error:'), (error as Error).message);
      process.exit(1);
    }
  });

// Configuration management commands
program
  .command('config')
  .description('Manage agent configuration')
  .option('--show', 'Show current configuration')
  .option('--reset', 'Reset configuration')
  .action(async (options) => {
    const configManager = new ConfigManager();

    if (options.show) {
      try {
        const config = await configManager.loadConfig();
        console.log(chalk.blue('Current Configuration:'));
        console.log(JSON.stringify({
          provider: config.provider,
          model: config.model,
          baseUrl: config.baseUrl,
          temperature: config.temperature,
          maxTokens: config.maxTokens
        }, null, 2));
      } catch {
        console.log(chalk.yellow('No configuration found'));
      }
    } else if (options.reset) {
      await configManager.resetConfig();
      console.log(chalk.green('Configuration reset successfully'));
    } else {
      const config = await setupConfiguration({});
      await configManager.saveConfig(config);
      console.log(chalk.green('Configuration saved successfully'));
    }
  });

// Setup configuration interactively
async function setupConfiguration(options: any): Promise<AgentConfig> {
  console.log(chalk.blue('\n🤖 Agent Configuration Setup\n'));

  const questions = [
    {
      type: 'list',
      name: 'provider',
      message: 'Select AI provider:',
      choices: [
        { name: 'OpenAI', value: 'openai' },
        { name: 'Deepseek', value: 'deepseek' },
        { name: 'Ollama (Local)', value: 'ollama' }
      ],
      default: options.provider || 'openai'
    },
    {
      type: 'input',
      name: 'apiKey',
      message: 'Enter API key:',
      when: (answers: any) => answers.provider !== 'ollama',
      validate: (input: string) => input.length > 0 || 'API key is required'
    },
    {
      type: 'input',
      name: 'baseUrl',
      message: 'Enter base URL (optional):',
      when: (answers: any) => answers.provider !== 'openai'
    },
    {
      type: 'input',
      name: 'model',
      message: 'Enter model name:',
      default: (answers: any) => {
        switch (answers.provider) {
          case 'openai': return 'gpt-4-turbo-preview';
          case 'deepseek': return 'deepseek-reasoner';
          case 'ollama': return 'llama2';
          default: return 'gpt-4-turbo-preview';
        }
      }
    },
    {
      type: 'number',
      name: 'temperature',
      message: 'Temperature (0.0-2.0):',
      default: 0.7,
      validate: (input: number) => input >= 0 && input <= 2
    },
    {
      type: 'number',
      name: 'maxTokens',
      message: 'Max tokens (optional):',
      default: 4000
    }
  ];

  const answers = await inquirer.prompt(questions);

  return {
    provider: answers.provider,
    apiKey: answers.apiKey || 'ollama',
    baseUrl: answers.baseUrl,
    model: answers.model,
    temperature: answers.temperature,
    maxTokens: answers.maxTokens
  };
}

// Error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\nGracefully shutting down...'));
  process.exit(0);
});

program.parse();
