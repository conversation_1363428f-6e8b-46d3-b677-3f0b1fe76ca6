// Main exports for the Autonomous Agent CLI
export { AgentEngine } from './core/AgentEngine';
export { AIProvider } from './core/ai/AIProvider';
export { ToolRegistry } from './core/tools/ToolRegistry';
export { ContextManager } from './core/context/ContextManager';
export { ConfigManager } from './core/config/ConfigManager';
export { DiffTracker } from './core/diff/DiffTracker';
export { SemanticSearchEngine } from './core/semantic/SemanticSearchEngine';
export { GitManager } from './core/git/GitManager';
export { TerminalUI } from './ui/TerminalUI';

// Export types
export * from './types';

// Export tools
export * from './tools/FileOperations';
export * from './tools/ShellCommands';
export * from './tools/GitOperations';
export * from './tools/SemanticSearch';
export * from './tools/ContextManagement';

// Version
export const VERSION = '1.0.0';

// Default configuration
export const DEFAULT_CONFIG = {
  provider: 'openai' as const,
  model: 'gpt-4-turbo-preview',
  temperature: 0.7,
  maxTokens: 4000
};
