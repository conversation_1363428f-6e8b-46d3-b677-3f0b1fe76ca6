import { z } from 'zod';

// Core Agent Types
export interface AgentConfig {
  provider: AIProvider;
  apiKey: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

export type AIProvider = 'openai' | 'deepseek' | 'ollama';

export interface AgentContext {
  sessionId: string;
  workingDirectory: string;
  gitRepository?: GitRepository;
  conversationHistory: ConversationMessage[];
  activeTools: string[];
  changeHistory: ChangeRecord[];
  semanticIndex?: SemanticIndex;
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
}

// Tool System Types
export interface Tool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  execute: (params: any, context: AgentContext) => Promise<ToolResult>;
  category: ToolCategory;
  requiresConfirmation?: boolean;
}

export type ToolCategory = 
  | 'file-operations' 
  | 'shell-commands' 
  | 'git-operations' 
  | 'semantic-search' 
  | 'debugging' 
  | 'context-management';

export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
  timestamp: Date;
}

export interface ToolResult {
  id: string;
  toolCallId: string;
  success: boolean;
  output: any;
  error?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

// Git Integration Types
export interface GitRepository {
  path: string;
  currentBranch: string;
  remotes: GitRemote[];
  status: GitStatus;
  lastCommit?: GitCommit;
}

export interface GitRemote {
  name: string;
  url: string;
}

export interface GitStatus {
  modified: string[];
  added: string[];
  deleted: string[];
  untracked: string[];
  staged: string[];
}

export interface GitCommit {
  hash: string;
  message: string;
  author: string;
  date: Date;
}

// Diff and Change Tracking Types
export interface ChangeRecord {
  id: string;
  type: 'file-create' | 'file-modify' | 'file-delete' | 'git-operation';
  path: string;
  timestamp: Date;
  diff?: DiffResult;
  metadata?: Record<string, any>;
}

export interface DiffResult {
  oldContent?: string;
  newContent?: string;
  hunks: DiffHunk[];
  stats: DiffStats;
}

export interface DiffHunk {
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  lines: DiffLine[];
}

export interface DiffLine {
  type: 'add' | 'remove' | 'context';
  content: string;
  lineNumber?: number;
}

export interface DiffStats {
  additions: number;
  deletions: number;
  changes: number;
}

// Semantic Search Types
export interface SemanticIndex {
  embeddings: Map<string, CodeEmbedding>;
  lastUpdated: Date;
  version: string;
}

export interface CodeEmbedding {
  filePath: string;
  content: string;
  embedding: number[];
  metadata: {
    language: string;
    symbols: string[];
    imports: string[];
    exports: string[];
  };
  lastUpdated: Date;
}

export interface SemanticSearchResult {
  filePath: string;
  content: string;
  score: number;
  context: string;
  lineNumbers: [number, number];
}

// Debugging Types
export interface DebugSession {
  id: string;
  processId?: number;
  language: string;
  status: 'active' | 'paused' | 'terminated';
  breakpoints: Breakpoint[];
  stackTrace?: StackFrame[];
  variables?: Variable[];
}

export interface Breakpoint {
  id: string;
  filePath: string;
  lineNumber: number;
  condition?: string;
  enabled: boolean;
}

export interface StackFrame {
  id: string;
  name: string;
  filePath: string;
  lineNumber: number;
  columnNumber?: number;
}

export interface Variable {
  name: string;
  value: any;
  type: string;
  scope: 'local' | 'global' | 'parameter';
}

// Error and Event Types
export class AgentError extends Error {
  public code: string;
  public category: 'tool-execution' | 'ai-communication' | 'system' | 'user-input';
  public recoverable: boolean;
  public context?: Record<string, any>;

  constructor(
    message: string,
    code: string,
    category: 'tool-execution' | 'ai-communication' | 'system' | 'user-input',
    recoverable: boolean,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AgentError';
    this.code = code;
    this.category = category;
    this.recoverable = recoverable;
    this.context = context;
  }
}

export interface AgentEvent {
  type: string;
  data: any;
  timestamp: Date;
  source: string;
}

// Configuration Schemas
export const AgentConfigSchema = z.object({
  provider: z.enum(['openai', 'deepseek', 'ollama']),
  apiKey: z.string().min(1),
  baseUrl: z.string().url().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
  model: z.string().min(1),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
});

export const ToolCallSchema = z.object({
  id: z.string(),
  name: z.string(),
  parameters: z.record(z.any()),
});
