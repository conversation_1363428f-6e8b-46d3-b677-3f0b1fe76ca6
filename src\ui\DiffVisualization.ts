import chalk from 'chalk';
import stripAnsi from 'strip-ansi';
import { DiffResult, DiffHunk, DiffLine, ChangeRecord } from '../types';

export interface DiffDisplayOptions {
  maxLines?: number;
  showLineNumbers?: boolean;
  showStats?: boolean;
  colorize?: boolean;
  contextLines?: number;
  showHeader?: boolean;
  compact?: boolean;
}

export class DiffVisualization {
  private static readonly DEFAULT_OPTIONS: DiffDisplayOptions = {
    maxLines: 50,
    showLineNumbers: true,
    showStats: true,
    colorize: true,
    contextLines: 3,
    showHeader: true,
    compact: false
  };

  /**
   * Render a diff for terminal display
   */
  static renderDiff(diff: DiffResult, filePath: string, options: DiffDisplayOptions = {}): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const lines: string[] = [];

    if (opts.showHeader) {
      lines.push(this.renderHeader(filePath, diff, opts));
    }

    if (opts.showStats) {
      lines.push(this.renderStats(diff.stats, opts));
    }

    // Render hunks
    let totalLines = 0;
    for (const hunk of diff.hunks) {
      if (opts.maxLines && totalLines >= opts.maxLines) {
        lines.push(this.colorize('... (truncated)', 'gray', opts));
        break;
      }

      const hunkLines = this.renderHunk(hunk, opts);
      lines.push(...hunkLines);
      totalLines += hunkLines.length;
    }

    return lines.join('\n');
  }

  /**
   * Render diff header
   */
  private static renderHeader(filePath: string, diff: DiffResult, options: DiffDisplayOptions): string {
    const header = `diff --git a/${filePath} b/${filePath}`;
    return options.colorize ? chalk.bold(header) : header;
  }

  /**
   * Render diff statistics
   */
  private static renderStats(stats: any, options: DiffDisplayOptions): string {
    const { additions, deletions, changes } = stats;
    const parts: string[] = [];

    if (additions > 0) {
      const addText = `+${additions}`;
      parts.push(options.colorize ? chalk.green(addText) : addText);
    }

    if (deletions > 0) {
      const delText = `-${deletions}`;
      parts.push(options.colorize ? chalk.red(delText) : delText);
    }

    if (changes > 0) {
      const changeText = `~${changes}`;
      parts.push(options.colorize ? chalk.yellow(changeText) : changeText);
    }

    const statsLine = parts.length > 0 ? `(${parts.join(' ')})` : '(no changes)';
    return options.colorize ? chalk.dim(statsLine) : statsLine;
  }

  /**
   * Render a single diff hunk
   */
  private static renderHunk(hunk: DiffHunk, options: DiffDisplayOptions): string[] {
    const lines: string[] = [];

    // Hunk header
    const hunkHeader = `@@ -${hunk.oldStart},${hunk.oldLines} +${hunk.newStart},${hunk.newLines} @@`;
    lines.push(this.colorize(hunkHeader, 'cyan', options));

    // Hunk lines
    let oldLineNum = hunk.oldStart;
    let newLineNum = hunk.newStart;

    for (const line of hunk.lines) {
      const renderedLine = this.renderDiffLine(line, oldLineNum, newLineNum, options);
      lines.push(renderedLine);

      // Update line numbers
      if (line.type === 'remove' || line.type === 'context') {
        oldLineNum++;
      }
      if (line.type === 'add' || line.type === 'context') {
        newLineNum++;
      }
    }

    return lines;
  }

  /**
   * Render a single diff line
   */
  private static renderDiffLine(
    line: DiffLine, 
    oldLineNum: number, 
    newLineNum: number, 
    options: DiffDisplayOptions
  ): string {
    let prefix = ' ';
    let color = 'white';
    let lineNumStr = '';

    // Determine prefix and color
    switch (line.type) {
      case 'add':
        prefix = '+';
        color = 'green';
        break;
      case 'remove':
        prefix = '-';
        color = 'red';
        break;
      case 'context':
        prefix = ' ';
        color = 'white';
        break;
    }

    // Build line number display
    if (options.showLineNumbers) {
      const oldNum = line.type === 'add' ? '   ' : oldLineNum.toString().padStart(3);
      const newNum = line.type === 'remove' ? '   ' : newLineNum.toString().padStart(3);
      lineNumStr = options.colorize 
        ? chalk.dim(`${oldNum} ${newNum} `) 
        : `${oldNum} ${newNum} `;
    }

    // Build content
    const content = `${prefix}${line.content}`;
    const colorizedContent = this.colorize(content, color, options);

    return `${lineNumStr}${colorizedContent}`;
  }

  /**
   * Render multiple changes as a summary
   */
  static renderChangesSummary(changes: ChangeRecord[], options: DiffDisplayOptions = {}): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const lines: string[] = [];

    if (changes.length === 0) {
      return this.colorize('No changes tracked', 'gray', opts);
    }

    lines.push(this.colorize(`\n📋 Change Summary (${changes.length} changes)`, 'bold', opts));
    lines.push(this.colorize('─'.repeat(50), 'gray', opts));

    for (const change of changes.slice(-10)) { // Show last 10 changes
      const timeStr = change.timestamp.toLocaleTimeString();
      const typeIcon = this.getChangeTypeIcon(change.type);
      const pathStr = change.path.length > 40 
        ? '...' + change.path.slice(-37) 
        : change.path;

      let line = `${typeIcon} ${pathStr}`;
      
      if (change.diff) {
        const stats = this.renderStats(change.diff.stats, opts);
        line += ` ${stats}`;
      }
      
      line += this.colorize(` (${timeStr})`, 'gray', opts);
      lines.push(line);
    }

    return lines.join('\n');
  }

  /**
   * Render side-by-side diff comparison
   */
  static renderSideBySide(diff: DiffResult, filePath: string, options: DiffDisplayOptions = {}): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const lines: string[] = [];
    const terminalWidth = process.stdout.columns || 120;
    const halfWidth = Math.floor((terminalWidth - 10) / 2);

    if (opts.showHeader) {
      lines.push(this.renderHeader(filePath, diff, opts));
      lines.push(this.colorize('─'.repeat(terminalWidth), 'gray', opts));
    }

    // Split content into old and new
    const oldLines = diff.oldContent?.split('\n') || [];
    const newLines = diff.newContent?.split('\n') || [];
    const maxLines = Math.max(oldLines.length, newLines.length);

    // Header for side-by-side
    const oldHeader = 'Original'.padEnd(halfWidth);
    const newHeader = 'Modified'.padEnd(halfWidth);
    lines.push(
      this.colorize(oldHeader, 'red', opts) + 
      ' │ ' + 
      this.colorize(newHeader, 'green', opts)
    );
    lines.push(this.colorize('─'.repeat(terminalWidth), 'gray', opts));

    // Render lines side by side
    for (let i = 0; i < Math.min(maxLines, opts.maxLines || 50); i++) {
      const oldLine = (oldLines[i] || '').substring(0, halfWidth - 3).padEnd(halfWidth);
      const newLine = (newLines[i] || '').substring(0, halfWidth - 3).padEnd(halfWidth);
      
      const oldColored = oldLines[i] !== newLines[i] 
        ? this.colorize(oldLine, 'red', opts)
        : oldLine;
      const newColored = oldLines[i] !== newLines[i] 
        ? this.colorize(newLine, 'green', opts)
        : newLine;

      lines.push(`${oldColored} │ ${newColored}`);
    }

    if (maxLines > (opts.maxLines || 50)) {
      lines.push(this.colorize('... (truncated)', 'gray', opts));
    }

    return lines.join('\n');
  }

  /**
   * Render compact diff (one-line summary)
   */
  static renderCompact(diff: DiffResult, filePath: string, options: DiffDisplayOptions = {}): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const stats = this.renderStats(diff.stats, opts);
    const fileName = filePath.split('/').pop() || filePath;
    
    return `${this.getChangeTypeIcon('file-modify')} ${fileName} ${stats}`;
  }

  /**
   * Get icon for change type
   */
  private static getChangeTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      'file-create': '📄',
      'file-modify': '✏️',
      'file-delete': '🗑️',
      'git-operation': '🔀'
    };
    return icons[type] || '📝';
  }

  /**
   * Apply color if colorization is enabled
   */
  private static colorize(text: string, color: string, options: DiffDisplayOptions): string {
    if (!options.colorize) {
      return text;
    }

    switch (color) {
      case 'red': return chalk.red(text);
      case 'green': return chalk.green(text);
      case 'yellow': return chalk.yellow(text);
      case 'blue': return chalk.blue(text);
      case 'cyan': return chalk.cyan(text);
      case 'magenta': return chalk.magenta(text);
      case 'gray': return chalk.gray(text);
      case 'dim': return chalk.dim(text);
      case 'bold': return chalk.bold(text);
      default: return text;
    }
  }

  /**
   * Create a diff visualization for terminal display
   */
  static createTerminalDiff(
    oldContent: string, 
    newContent: string, 
    filePath: string, 
    options: DiffDisplayOptions = {}
  ): string {
    // This would integrate with the DiffTracker to generate the actual diff
    const mockDiff: DiffResult = {
      oldContent,
      newContent,
      hunks: [], // Would be populated by actual diff algorithm
      stats: {
        additions: newContent.split('\n').length - oldContent.split('\n').length,
        deletions: 0,
        changes: 1
      }
    };

    return this.renderDiff(mockDiff, filePath, options);
  }

  /**
   * Format diff for streaming output
   */
  static formatForStreaming(diff: DiffResult, filePath: string): string {
    return this.renderCompact(diff, filePath, { 
      colorize: true, 
      showStats: true 
    });
  }

  /**
   * Get terminal width for responsive rendering
   */
  static getTerminalWidth(): number {
    return process.stdout.columns || 120;
  }

  /**
   * Strip ANSI colors for plain text output
   */
  static stripColors(text: string): string {
    return stripAnsi(text);
  }
}
