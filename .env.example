# Autonomous Agent CLI Configuration

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=4000

# Deepseek Configuration
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_MAX_TOKENS=4000

# Ollama Configuration (Local)
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_MODEL=llama2
OLLAMA_TEMPERATURE=0.7
OLLAMA_MAX_TOKENS=4000

# Default Provider (openai, deepseek, or ollama)
DEFAULT_PROVIDER=openai

# Agent Configuration
AGENT_WORKING_DIRECTORY=./
AGENT_LOG_LEVEL=info
AGENT_MAX_ITERATIONS=50
AGENT_TIMEOUT=300000

# Development Settings
NODE_ENV=development
DEBUG=false
