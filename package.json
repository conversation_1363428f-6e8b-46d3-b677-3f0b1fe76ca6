{"name": "autonomous-agent-cli", "version": "1.0.0", "description": "Fully autonomous AI-powered CLI tool with semantic understanding and git integration", "main": "dist/index.js", "bin": {"agent": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsx src/cli.ts", "start": "node dist/cli.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["ai", "cli", "autonomous", "agent", "semantic-search", "git-integration", "debugging"], "author": "Your Name", "license": "MIT", "dependencies": {"chalk": "^5.3.0", "chokidar": "^3.5.3", "commander": "^11.1.0", "diff": "^5.1.0", "dotenv": "^16.3.1", "inquirer": "^9.2.12", "nanoid": "^5.0.4", "openai": "^5.1.0", "ora": "^7.0.1", "simple-git": "^3.20.0", "strip-ansi": "^7.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/diff": "^5.0.8", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.5", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.51.0", "jest": "^29.7.0", "prettier": "^3.0.3", "tsx": "^4.0.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}