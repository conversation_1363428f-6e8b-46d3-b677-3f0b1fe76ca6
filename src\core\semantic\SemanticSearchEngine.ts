import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import { join, extname, relative } from 'path';
import { SemanticIndex, CodeEmbedding, SemanticSearchResult } from '../../types';

/**
 * Enhanced Semantic Search Engine for code understanding
 * Provides intelligent code search with symbol extraction and context awareness
 */
export class SemanticSearchEngine extends EventEmitter {
  private index: SemanticIndex | undefined;
  private workingDirectory: string = '';

  constructor(workingDirectory?: string) {
    super();
    if (workingDirectory) {
      this.workingDirectory = workingDirectory;
    }
  }

  /**
   * Initialize semantic index
   */
  async initializeIndex(): Promise<void> {
    this.index = {
      embeddings: new Map(),
      lastUpdated: new Date(),
      version: '2.0.0'
    };

    // Auto-index common code files if working directory is set
    if (this.workingDirectory) {
      await this.indexDirectory(this.workingDirectory);
    }

    this.emit('index-initialized');
  }

  /**
   * Index a directory recursively
   */
  async indexDirectory(dirPath: string): Promise<void> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = join(dirPath, entry.name);

        if (entry.isDirectory()) {
          // Skip common directories that shouldn't be indexed
          if (this.shouldSkipDirectory(entry.name)) {
            continue;
          }
          await this.indexDirectory(fullPath);
        } else if (entry.isFile()) {
          // Only index code files
          if (this.isCodeFile(entry.name)) {
            try {
              const content = await fs.readFile(fullPath, 'utf-8');
              await this.indexFile(fullPath, content);
            } catch (error) {
              // Skip files that can't be read
              console.warn(`Could not index file ${fullPath}:`, (error as Error).message);
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Could not index directory ${dirPath}:`, (error as Error).message);
    }
  }

  /**
   * Index a file for semantic search
   */
  async indexFile(filePath: string, content: string): Promise<void> {
    if (!this.index) {
      await this.initializeIndex();
    }

    const embedding: CodeEmbedding = {
      filePath,
      content,
      embedding: this.generateSimpleEmbedding(content), // Simple embedding for now
      metadata: {
        language: this.detectLanguage(filePath),
        symbols: this.extractSymbols(content),
        imports: this.extractImports(content),
        exports: this.extractExports(content)
      },
      lastUpdated: new Date()
    };

    this.index!.embeddings.set(filePath, embedding);
    this.emit('file-indexed', filePath);
  }

  /**
   * Search for code using natural language query
   */
  async search(query: string, limit: number = 10): Promise<SemanticSearchResult[]> {
    if (!this.index) {
      return [];
    }

    const results: SemanticSearchResult[] = [];
    const queryLower = query.toLowerCase();
    const queryTerms = this.extractSearchTerms(query);

    for (const [filePath, embedding] of this.index!.embeddings) {
      const scores = this.calculateRelevanceScores(embedding, queryTerms, queryLower);

      if (scores.totalScore > 0.1) { // Minimum relevance threshold
        const lines = embedding.content.split('\n');
        const matchingLines = this.findMatchingLines(lines, queryTerms, queryLower);

        for (const match of matchingLines.slice(0, 3)) {
          results.push({
            filePath: relative(this.workingDirectory, filePath),
            content: match.line.trim(),
            score: scores.totalScore + match.score,
            context: this.getContext(lines, match.index),
            lineNumbers: [match.index + 1, match.index + 1]
          });
        }
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Update index for a file
   */
  async updateFile(filePath: string, content: string): Promise<void> {
    await this.indexFile(filePath, content);
  }

  /**
   * Remove file from index
   */
  async removeFile(filePath: string): Promise<void> {
    if (this.index) {
      this.index.embeddings.delete(filePath);
      this.emit('file-removed', filePath);
    }
  }

  /**
   * Get index statistics
   */
  getIndexStats(): Record<string, any> {
    if (!this.index) {
      return { indexed: false };
    }

    const embeddings = Array.from(this.index.embeddings.values());
    const languages = embeddings.reduce((acc, emb) => {
      acc[emb.metadata.language] = (acc[emb.metadata.language] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      indexed: true,
      totalFiles: embeddings.length,
      lastUpdated: this.index.lastUpdated,
      version: this.index.version,
      languages
    };
  }

  /**
   * Detect programming language from file path
   */
  private detectLanguage(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell'
    };

    return languageMap[ext || ''] || 'text';
  }

  /**
   * Extract symbols from code (placeholder)
   */
  private extractSymbols(content: string): string[] {
    // Placeholder: simple regex-based extraction
    const symbols: string[] = [];
    
    // Function declarations
    const functionMatches = content.match(/function\s+(\w+)/g);
    if (functionMatches) {
      symbols.push(...functionMatches.map(m => m.replace('function ', '')));
    }

    // Class declarations
    const classMatches = content.match(/class\s+(\w+)/g);
    if (classMatches) {
      symbols.push(...classMatches.map(m => m.replace('class ', '')));
    }

    // Variable declarations
    const varMatches = content.match(/(?:const|let|var)\s+(\w+)/g);
    if (varMatches) {
      symbols.push(...varMatches.map(m => m.split(' ')[1]));
    }

    return [...new Set(symbols)]; // Remove duplicates
  }

  /**
   * Extract imports from code (placeholder)
   */
  private extractImports(content: string): string[] {
    const imports: string[] = [];
    
    // ES6 imports
    const importMatches = content.match(/import.*from\s+['"]([^'"]+)['"]/g);
    if (importMatches) {
      imports.push(...importMatches.map(m => {
        const match = m.match(/from\s+['"]([^'"]+)['"]/);
        return match ? match[1] : '';
      }).filter(Boolean));
    }

    // CommonJS requires
    const requireMatches = content.match(/require\(['"]([^'"]+)['"]\)/g);
    if (requireMatches) {
      imports.push(...requireMatches.map(m => {
        const match = m.match(/require\(['"]([^'"]+)['"]\)/);
        return match ? match[1] : '';
      }).filter(Boolean));
    }

    return [...new Set(imports)];
  }

  /**
   * Extract exports from code (placeholder)
   */
  private extractExports(content: string): string[] {
    const exports: string[] = [];
    
    // ES6 exports
    const exportMatches = content.match(/export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g);
    if (exportMatches) {
      exports.push(...exportMatches.map(m => {
        const parts = m.split(/\s+/);
        return parts[parts.length - 1];
      }));
    }

    // Named exports
    const namedExportMatches = content.match(/export\s*{\s*([^}]+)\s*}/g);
    if (namedExportMatches) {
      namedExportMatches.forEach(match => {
        const names = match.replace(/export\s*{\s*/, '').replace(/\s*}/, '');
        exports.push(...names.split(',').map(name => name.trim()));
      });
    }

    return [...new Set(exports)];
  }

  /**
   * Generate simple embedding based on content analysis
   */
  private generateSimpleEmbedding(content: string): number[] {
    // Simple TF-IDF-like embedding for basic semantic understanding
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    const wordFreq = new Map<string, number>();

    // Count word frequencies
    for (const word of words) {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    }

    // Create a simple vector based on common programming terms
    const programmingTerms = [
      'function', 'class', 'method', 'variable', 'const', 'let', 'var',
      'if', 'else', 'for', 'while', 'return', 'import', 'export',
      'async', 'await', 'promise', 'callback', 'event', 'handler',
      'component', 'props', 'state', 'render', 'hook', 'effect',
      'api', 'request', 'response', 'data', 'json', 'http',
      'error', 'exception', 'try', 'catch', 'throw', 'debug'
    ];

    return programmingTerms.map(term => wordFreq.get(term) || 0);
  }

  /**
   * Extract search terms from natural language query
   */
  private extractSearchTerms(query: string): string[] {
    // Remove common stop words and extract meaningful terms
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must']);

    return query.toLowerCase()
      .match(/\b\w+\b/g)
      ?.filter(word => !stopWords.has(word) && word.length > 2) || [];
  }

  /**
   * Calculate relevance scores for a code embedding
   */
  private calculateRelevanceScores(embedding: CodeEmbedding, queryTerms: string[], queryLower: string): { totalScore: number; symbolScore: number; contentScore: number } {
    let symbolScore = 0;
    let contentScore = 0;

    // Score based on symbol matches
    const allSymbols = [
      ...embedding.metadata.symbols,
      ...embedding.metadata.imports,
      ...embedding.metadata.exports
    ].map(s => s.toLowerCase());

    for (const term of queryTerms) {
      if (allSymbols.some(symbol => symbol.includes(term))) {
        symbolScore += 0.5;
      }
    }

    // Score based on content matches
    const contentLower = embedding.content.toLowerCase();
    for (const term of queryTerms) {
      const matches = (contentLower.match(new RegExp(term, 'g')) || []).length;
      contentScore += matches * 0.1;
    }

    // Bonus for exact phrase matches
    if (contentLower.includes(queryLower)) {
      contentScore += 0.3;
    }

    return {
      totalScore: symbolScore + contentScore,
      symbolScore,
      contentScore
    };
  }

  /**
   * Find matching lines in content
   */
  private findMatchingLines(lines: string[], queryTerms: string[], queryLower: string): Array<{ line: string; index: number; score: number }> {
    const matches: Array<{ line: string; index: number; score: number }> = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineLower = line.toLowerCase();
      let score = 0;

      // Check for query terms
      for (const term of queryTerms) {
        if (lineLower.includes(term)) {
          score += 0.2;
        }
      }

      // Check for exact phrase
      if (lineLower.includes(queryLower)) {
        score += 0.5;
      }

      if (score > 0) {
        matches.push({ line, index: i, score });
      }
    }

    return matches.sort((a, b) => b.score - a.score);
  }

  /**
   * Check if directory should be skipped during indexing
   */
  private shouldSkipDirectory(dirName: string): boolean {
    const skipDirs = new Set([
      'node_modules', '.git', '.svn', '.hg', 'dist', 'build', 'out',
      'target', 'bin', 'obj', '.next', '.nuxt', 'coverage', '.nyc_output',
      '__pycache__', '.pytest_cache', '.vscode', '.idea', '.vs',
      'vendor', 'deps', '_build', '.sass-cache'
    ]);

    return skipDirs.has(dirName) || dirName.startsWith('.');
  }

  /**
   * Check if file should be indexed
   */
  private isCodeFile(fileName: string): boolean {
    const codeExtensions = new Set([
      '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs',
      '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.scala', '.html',
      '.css', '.scss', '.less', '.json', '.xml', '.yaml', '.yml',
      '.md', '.sh', '.bash', '.zsh', '.fish', '.ps1', '.sql'
    ]);

    const ext = extname(fileName).toLowerCase();
    return codeExtensions.has(ext);
  }

  /**
   * Get context around a line
   */
  private getContext(lines: string[], lineIndex: number, contextSize: number = 2): string {
    const start = Math.max(0, lineIndex - contextSize);
    const end = Math.min(lines.length, lineIndex + contextSize + 1);

    return lines.slice(start, end).join('\n');
  }

  /**
   * Clear the entire index
   */
  async clearIndex(): Promise<void> {
    if (this.index) {
      this.index.embeddings.clear();
      this.index.lastUpdated = new Date();
      this.emit('index-cleared');
    }
  }

  /**
   * Check if index exists
   */
  hasIndex(): boolean {
    return !!this.index;
  }
}
