import OpenAI from 'openai';
import { 
  AgentConfig, 
  AgentContext, 
  ConversationMessage, 
  ToolCall,
  AIProvider as AIProviderType 
} from '../../types';
import { z } from 'zod';

export interface AIResponse {
  content: string;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * Universal AI Provider using OpenAI SDK 5.0.0
 * Supports OpenAI, Deepseek, and Ollama through compatible APIs
 */
export class AIProvider {
  private client!: OpenAI;
  private config: AgentConfig;

  constructor(config: AgentConfig) {
    this.config = config;
    this.initializeClient();
  }

  private initializeClient(): void {
    const clientConfig: any = {
      apiKey: this.config.apiKey,
    };

    // Configure provider-specific endpoints
    switch (this.config.provider) {
      case 'openai':
        // Default OpenAI configuration
        break;
      
      case 'deepseek':
        clientConfig.baseURL = this.config.baseUrl || 'https://api.deepseek.com/v1';
        break;
      
      case 'ollama':
        clientConfig.baseURL = this.config.baseUrl || 'http://localhost:11434/v1';
        clientConfig.apiKey = 'ollama'; // Ollama doesn't require real API key
        break;
      
      default:
        throw new Error(`Unsupported AI provider: ${this.config.provider}`);
    }

    this.client = new OpenAI(clientConfig);
  }

  /**
   * Get AI response with streaming support and tool calls
   */
  async getResponse(
    conversationHistory: ConversationMessage[],
    toolSchemas: any[],
    context: AgentContext
  ): Promise<AIResponse> {
    try {
      const messages = this.formatMessages(conversationHistory, context);
      const tools = this.formatToolSchemas(toolSchemas);

      const completion = await this.client.chat.completions.create({
        model: this.config.model,
        messages,
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 4000,
        stream: false, // We'll implement streaming separately
      });

      return this.parseResponse(completion);

    } catch (error) {
      const errorMessage = this.formatAIError(error as Error);
      throw new Error(`AI Provider Error: ${errorMessage}`);
    }
  }

  /**
   * Get streaming AI response
   */
  async getStreamingResponse(
    conversationHistory: ConversationMessage[],
    toolSchemas: any[],
    context: AgentContext,
    onChunk: (chunk: string) => void,
    onToolCall: (toolCall: ToolCall) => void
  ): Promise<AIResponse> {
    try {
      const messages = this.formatMessages(conversationHistory, context);
      const tools = this.formatToolSchemas(toolSchemas);

      const stream = await this.client.chat.completions.create({
        model: this.config.model,
        messages,
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 4000,
        stream: true,
      });

      let content = '';
      let toolCalls: ToolCall[] = [];
      let usage: any = undefined;

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;

        if (delta?.content) {
          content += delta.content;
          onChunk(delta.content);
        }

        if (delta?.tool_calls) {
          for (const toolCall of delta.tool_calls) {
            if (toolCall.function) {
              const parsedToolCall: ToolCall = {
                id: toolCall.id || `tool_${Date.now()}`,
                name: toolCall.function.name || 'unknown',
                parameters: JSON.parse(toolCall.function.arguments || '{}'),
                timestamp: new Date()
              };
              toolCalls.push(parsedToolCall);
              onToolCall(parsedToolCall);
            }
          }
        }

        if (chunk.usage) {
          usage = chunk.usage;
        }
      }

      return {
        content,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        usage: usage ? {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens
        } : undefined
      };

    } catch (error) {
      const errorMessage = this.formatAIError(error as Error);
      throw new Error(`AI Streaming Error: ${errorMessage}`);
    }
  }

  /**
   * Format conversation messages for AI provider
   */
  private formatMessages(
    conversationHistory: ConversationMessage[],
    context: AgentContext
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    const systemMessage: OpenAI.Chat.Completions.ChatCompletionSystemMessageParam = {
      role: 'system',
      content: this.buildSystemPrompt(context)
    };

    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [systemMessage];

    for (const msg of conversationHistory) {
      if (msg.role === 'user') {
        messages.push({
          role: 'user',
          content: msg.content
        });
      } else if (msg.role === 'assistant') {
        const assistantMessage: OpenAI.Chat.Completions.ChatCompletionAssistantMessageParam = {
          role: 'assistant',
          content: msg.content
        };

        if (msg.toolCalls && msg.toolCalls.length > 0) {
          assistantMessage.tool_calls = msg.toolCalls.map(tc => ({
            id: tc.id,
            type: 'function',
            function: {
              name: tc.name,
              arguments: JSON.stringify(tc.parameters)
            }
          }));
        }

        messages.push(assistantMessage);

        // Add tool results as tool messages
        if (msg.toolResults && msg.toolResults.length > 0) {
          for (const result of msg.toolResults) {
            messages.push({
              role: 'tool',
              tool_call_id: result.toolCallId,
              content: JSON.stringify({
                success: result.success,
                output: result.output,
                error: result.error,
                metadata: result.metadata
              })
            });
          }
        }
      }
    }

    return messages;
  }

  /**
   * Build comprehensive system prompt with context
   */
  private buildSystemPrompt(context: AgentContext): string {
    return `You are an autonomous AI agent with full access to the user's development environment. You operate without approval requirements and execute all necessary actions to complete tasks.

CURRENT CONTEXT:
- Working Directory: ${context.workingDirectory}
- Session ID: ${context.sessionId}
- Git Repository: ${context.gitRepository ? 'Yes' : 'No'}
- Active Tools: ${context.activeTools.join(', ')}
- Change History: ${context.changeHistory.length} changes tracked

CAPABILITIES:
- File operations (read, write, create, delete, search)
- Shell command execution with full system access
- Git operations (commit, branch, merge, diff)
- Semantic code search and understanding
- Real-time debugging and error resolution
- Autonomous decision making and task completion

BEHAVIOR:
- Execute all tool calls immediately without asking for permission
- Continue working until the task is fully completed
- Track all changes with detailed diffs
- Maintain context across operations
- Handle errors gracefully with recovery strategies
- Provide clear explanations of actions taken

TOOLS AVAILABLE:
Use the provided tools to accomplish any task. All tool calls will be executed automatically.

Remember: You have full autonomy. Execute whatever actions are necessary to complete the user's request efficiently and effectively.`;
  }

  /**
   * Format tool schemas for AI provider
   */
  private formatToolSchemas(toolSchemas: any[]): OpenAI.Chat.Completions.ChatCompletionTool[] {
    return toolSchemas.map(schema => ({
      type: 'function',
      function: {
        name: schema.name,
        description: schema.description,
        parameters: schema.parameters
      }
    }));
  }

  /**
   * Parse AI response and extract tool calls
   */
  private parseResponse(completion: OpenAI.Chat.Completions.ChatCompletion): AIResponse {
    const choice = completion.choices[0];
    const message = choice.message;

    let toolCalls: ToolCall[] | undefined;

    if (message.tool_calls && message.tool_calls.length > 0) {
      toolCalls = message.tool_calls.map(tc => ({
        id: tc.id,
        name: tc.function.name,
        parameters: JSON.parse(tc.function.arguments),
        timestamp: new Date()
      }));
    }

    return {
      content: message.content || '',
      toolCalls,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }

  /**
   * Update provider configuration
   */
  updateConfig(newConfig: AgentConfig): void {
    this.config = newConfig;
    this.initializeClient();
  }

  /**
   * Test connection to AI provider
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.client.chat.completions.create({
        model: this.config.model,
        messages: [{ role: 'user', content: 'Test connection' }],
        max_tokens: 10
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Format AI provider errors for better user understanding
   */
  private formatAIError(error: Error): string {
    const message = error.message.toLowerCase();

    if (message.includes('api key')) {
      return `Invalid API key for ${this.config.provider}. Please check your API key configuration.`;
    }

    if (message.includes('model') && message.includes('not found')) {
      return `Model '${this.config.model}' not found for ${this.config.provider}. Please check the model name.`;
    }

    if (message.includes('rate limit')) {
      return `Rate limit exceeded for ${this.config.provider}. Please wait and try again.`;
    }

    if (message.includes('quota')) {
      return `Quota exceeded for ${this.config.provider}. Please check your account limits.`;
    }

    if (message.includes('connection') || message.includes('network')) {
      return `Connection failed to ${this.config.provider}. Please check your internet connection and base URL.`;
    }

    if (message.includes('unauthorized')) {
      return `Unauthorized access to ${this.config.provider}. Please verify your API key and permissions.`;
    }

    // Return original error message if no specific pattern matches
    return error.message;
  }
}
