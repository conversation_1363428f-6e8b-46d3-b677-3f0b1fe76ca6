import { EventEmitter } from 'events';
import simpleGit, { SimpleGit, StatusResult } from 'simple-git';
import { GitRepository, GitRemote, GitStatus, GitCommit } from '../../types';

export class GitManager extends EventEmitter {
  private git: SimpleGit;
  private workingDirectory: string;

  constructor(workingDirectory: string) {
    super();
    this.workingDirectory = workingDirectory;
    this.git = simpleGit(workingDirectory);
  }

  /**
   * Get repository information
   */
  async getRepositoryInfo(): Promise<GitRepository> {
    try {
      // Check if we're in a git repository
      const isRepo = await this.git.checkIsRepo();
      if (!isRepo) {
        throw new Error('Not a git repository');
      }

      // Get current branch
      const currentBranch = await this.git.revparse(['--abbrev-ref', 'HEAD']);

      // Get remotes
      const remoteNames = await this.git.getRemotes();
      const remotes: GitRemote[] = await Promise.all(
        remoteNames.map(async (remote) => {
          const url = await this.git.getRemotes(true);
          const remoteInfo = url.find(r => r.name === remote.name);
          return {
            name: remote.name,
            url: remoteInfo?.refs?.fetch || ''
          };
        })
      );

      // Get status
      const status = await this.getStatus();

      // Get last commit
      const lastCommit = await this.getLastCommit();

      return {
        path: this.workingDirectory,
        currentBranch: currentBranch.trim(),
        remotes,
        status,
        lastCommit
      };
    } catch (error) {
      throw new Error(`Failed to get repository info: ${(error as Error).message}`);
    }
  }

  /**
   * Get repository status
   */
  async getStatus(): Promise<GitStatus> {
    try {
      const status: StatusResult = await this.git.status();

      return {
        modified: status.modified,
        added: status.created,
        deleted: status.deleted,
        untracked: status.not_added,
        staged: status.staged
      };
    } catch (error) {
      throw new Error(`Failed to get git status: ${(error as Error).message}`);
    }
  }

  /**
   * Get last commit information
   */
  async getLastCommit(): Promise<GitCommit | undefined> {
    try {
      const log = await this.git.log({ maxCount: 1 });
      const latest = log.latest;

      if (!latest) {
        return undefined;
      }

      return {
        hash: latest.hash,
        message: latest.message,
        author: latest.author_name,
        date: new Date(latest.date)
      };
    } catch (error) {
      // Repository might not have any commits yet
      return undefined;
    }
  }

  /**
   * Stage files
   */
  async stageFiles(files: string[]): Promise<void> {
    try {
      await this.git.add(files);
      this.emit('git-operation', {
        type: 'stage',
        files,
        message: `Staged ${files.length} files`
      });
    } catch (error) {
      throw new Error(`Failed to stage files: ${(error as Error).message}`);
    }
  }

  /**
   * Stage all changes
   */
  async stageAll(): Promise<void> {
    try {
      await this.git.add('.');
      this.emit('git-operation', {
        type: 'stage-all',
        message: 'Staged all changes'
      });
    } catch (error) {
      throw new Error(`Failed to stage all changes: ${(error as Error).message}`);
    }
  }

  /**
   * Commit changes
   */
  async commit(message: string): Promise<GitCommit> {
    try {
      const result = await this.git.commit(message);
      
      const commit: GitCommit = {
        hash: result.commit,
        message,
        author: 'Agent', // This would be configured
        date: new Date()
      };

      this.emit('git-operation', {
        type: 'commit',
        commit,
        message: `Committed: ${message}`
      });

      return commit;
    } catch (error) {
      throw new Error(`Failed to commit: ${(error as Error).message}`);
    }
  }

  /**
   * Create and switch to new branch
   */
  async createBranch(branchName: string, switchTo: boolean = true): Promise<void> {
    try {
      await this.git.checkoutLocalBranch(branchName);
      
      this.emit('git-operation', {
        type: 'create-branch',
        branch: branchName,
        message: `Created branch: ${branchName}`
      });
    } catch (error) {
      throw new Error(`Failed to create branch: ${(error as Error).message}`);
    }
  }

  /**
   * Switch to branch
   */
  async switchBranch(branchName: string): Promise<void> {
    try {
      await this.git.checkout(branchName);
      
      this.emit('git-operation', {
        type: 'switch-branch',
        branch: branchName,
        message: `Switched to branch: ${branchName}`
      });
    } catch (error) {
      throw new Error(`Failed to switch branch: ${(error as Error).message}`);
    }
  }

  /**
   * Get list of branches
   */
  async getBranches(): Promise<string[]> {
    try {
      const branches = await this.git.branchLocal();
      return branches.all;
    } catch (error) {
      throw new Error(`Failed to get branches: ${(error as Error).message}`);
    }
  }

  /**
   * Pull changes from remote
   */
  async pull(remote: string = 'origin', branch?: string): Promise<void> {
    try {
      await this.git.pull(remote, branch);
      
      this.emit('git-operation', {
        type: 'pull',
        remote,
        branch,
        message: `Pulled from ${remote}${branch ? `/${branch}` : ''}`
      });
    } catch (error) {
      throw new Error(`Failed to pull: ${(error as Error).message}`);
    }
  }

  /**
   * Push changes to remote
   */
  async push(remote: string = 'origin', branch?: string): Promise<void> {
    try {
      await this.git.push(remote, branch);
      
      this.emit('git-operation', {
        type: 'push',
        remote,
        branch,
        message: `Pushed to ${remote}${branch ? `/${branch}` : ''}`
      });
    } catch (error) {
      throw new Error(`Failed to push: ${(error as Error).message}`);
    }
  }

  /**
   * Get diff for files
   */
  async getDiff(files?: string[]): Promise<string> {
    try {
      if (files && files.length > 0) {
        return await this.git.diff(files);
      } else {
        return await this.git.diff();
      }
    } catch (error) {
      throw new Error(`Failed to get diff: ${(error as Error).message}`);
    }
  }

  /**
   * Get diff between commits
   */
  async getDiffBetweenCommits(commit1: string, commit2: string): Promise<string> {
    try {
      return await this.git.diff([`${commit1}..${commit2}`]);
    } catch (error) {
      throw new Error(`Failed to get diff between commits: ${(error as Error).message}`);
    }
  }

  /**
   * Initialize new git repository
   */
  async initRepository(): Promise<void> {
    try {
      await this.git.init();
      
      this.emit('git-operation', {
        type: 'init',
        message: 'Initialized git repository'
      });
    } catch (error) {
      throw new Error(`Failed to initialize repository: ${(error as Error).message}`);
    }
  }

  /**
   * Check if directory is a git repository
   */
  async isRepository(): Promise<boolean> {
    try {
      return await this.git.checkIsRepo();
    } catch {
      return false;
    }
  }

  /**
   * Get commit history
   */
  async getCommitHistory(limit: number = 10): Promise<GitCommit[]> {
    try {
      const log = await this.git.log({ maxCount: limit });
      
      return log.all.map(commit => ({
        hash: commit.hash,
        message: commit.message,
        author: commit.author_name,
        date: new Date(commit.date)
      }));
    } catch (error) {
      throw new Error(`Failed to get commit history: ${(error as Error).message}`);
    }
  }

  /**
   * Reset changes
   */
  async reset(mode: 'soft' | 'mixed' | 'hard' = 'mixed', commit?: string): Promise<void> {
    try {
      const resetOptions = [`--${mode}`];
      if (commit) {
        resetOptions.push(commit);
      }
      
      await this.git.reset(resetOptions);
      
      this.emit('git-operation', {
        type: 'reset',
        mode,
        commit,
        message: `Reset ${mode}${commit ? ` to ${commit}` : ''}`
      });
    } catch (error) {
      throw new Error(`Failed to reset: ${(error as Error).message}`);
    }
  }
}
