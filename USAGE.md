# Autonomous Agent CLI - Usage Guide

## 🎯 Overview

The Autonomous Agent CLI is a fully autonomous AI-powered tool that can understand, plan, and execute complex tasks without requiring user confirmation at each step. This guide provides comprehensive examples and best practices.

## 🚀 Getting Started

### 1. Quick Setup

```bash
# Clone and setup
git clone <repository-url>
cd autonomous-agent-cli
npm install
npm run build

# Start interactive mode
npx tsx src/cli.ts chat
```

### 2. Provider Configuration

The agent supports three AI providers:

#### OpenAI (Recommended)
```bash
npx tsx src/cli.ts chat --provider openai --api-key sk-your-key --model gpt-4-turbo-preview
```

#### Deepseek (Cost-effective)
```bash
npx tsx src/cli.ts chat --provider deepseek --api-key sk-your-key --model deepseek-chat
```

#### Ollama (Local/Private)
```bash
# First install Ollama and pull a model
ollama pull llama2

# Then run the agent
npx tsx src/cli.ts chat --provider ollama --model llama2
```

## 💡 Example Use Cases

### Development Tasks

#### Creating Components
```
🤖 > Create a new React component called UserProfile with TypeScript
🤖 > Add props for name, email, and avatar URL
🤖 > Include proper TypeScript interfaces and export them
```

#### Bug Fixing
```
🤖 > Find and fix the authentication bug in the login system
🤖 > Check for any TypeScript errors in the src directory
🤖 > Run the test suite and fix any failing tests
```

#### Code Analysis
```
🤖 > Search for all TODO comments in the codebase
🤖 > Find unused imports across all TypeScript files
🤖 > Analyze the code for potential security vulnerabilities
```

### Git Operations

#### Automated Commits
```
🤖 > Review all changes and commit them with descriptive messages
🤖 > Create a new feature branch for the user authentication system
🤖 > Merge the current branch into main after running tests
```

#### Code Review
```
🤖 > Show me the diff of the last 3 commits
🤖 > Analyze the recent changes for potential issues
🤖 > Generate a changelog from the last 10 commits
```

### Project Management

#### Setup and Configuration
```
🤖 > Initialize a new Node.js project with TypeScript
🤖 > Add ESLint and Prettier configuration
🤖 > Set up Jest for testing with TypeScript support
```

#### Documentation
```
🤖 > Generate API documentation from the TypeScript interfaces
🤖 > Create a README file for this project
🤖 > Update the package.json with proper metadata
```

## 🛠 Interactive Commands

### Built-in Commands

- `help` - Show available commands and examples
- `status` - Display agent status and context information
- `changes` - Show recent file changes with diffs
- `diff <id>` - Show detailed diff for a specific change
- `clear` - Clear the terminal screen
- `config show` - Display current configuration
- `exit` - Gracefully shutdown the agent

### Example Session

```
🤖 > help
📖 Available Commands:
  help       - Show this help message
  status     - Show agent status and context
  changes    - Show recent changes with diffs
  diff <id>  - Show detailed diff for a change
  clear      - Clear the terminal
  config     - Show configuration
  exit       - Exit the agent

💡 Example Requests:
  "Create a new React component called Button"
  "Fix the authentication bug in login.js"
  "Search for TODO comments in the codebase"
  "Run the test suite and fix any failures"
  "Commit all changes with a descriptive message"

🤖 > status
📊 Agent Status:
  Session ID: abc123
  Working Directory: /path/to/project
  Git Repository: Yes
  Active Tools: 26
  Conversation History: 5 messages
  Changes Tracked: 3
  Processing: No

🤖 > Create a simple Express.js server with TypeScript
🧠 Agent is thinking...
🔧 Executing: write_file
✓ write_file completed
💾 Created src/server.ts (1,234 bytes)
🔧 Executing: write_file
✓ write_file completed
💾 Created package.json (567 bytes)
✓ Task completed

🤖 > changes
📋 Recent Changes (2/2):
──────────────────────────────────────────────────────────
📝 src/server.ts (+45 -0) (10:30:15 AM)
📝 package.json (+15 -0) (10:30:16 AM)

Use "diff <change-id>" to see detailed diff
```

## 🔍 Advanced Features

### Semantic Code Search

The agent can understand code semantically, not just text matching:

```
🤖 > Find all functions that handle user authentication
🤖 > Show me components that use React hooks
🤖 > Search for database connection logic
```

### Autonomous Debugging

The agent can detect, analyze, and fix issues automatically:

```
🤖 > Debug the failing test in user.test.ts
🤖 > Fix the TypeScript compilation errors
🤖 > Analyze and resolve the memory leak in the server
```

### Git Integration

Full git workflow automation:

```
🤖 > Create a feature branch for the new payment system
🤖 > Review and commit all changes with proper messages
🤖 > Merge the feature branch and clean up
```

## 🎨 Diff Visualization

All changes are automatically tracked and visualized:

```
📝 file-create: src/components/Button.tsx
  +45 -0
  Preview:
    +import React from 'react';
    +
    +interface ButtonProps {
    +  children: React.ReactNode;
    +  onClick?: () => void;
    +}

✏️ file-modify: package.json
  +3 -1
  Preview:
    -  "dependencies": {}
    +  "dependencies": {
    +    "react": "^18.0.0"
    +  }
```

## 🔒 Safety and Security

### Best Practices

1. **Review Changes**: Use `changes` and `diff` commands to review what the agent has done
2. **Git Safety**: The agent preserves git history and creates meaningful commits
3. **Backup**: Always work in a git repository with committed changes
4. **Scope**: Start with small, specific tasks to understand the agent's behavior

### Security Features

- Complete audit trail of all operations
- Git operation safeguards
- Resource usage monitoring
- Comprehensive change tracking
- Error recovery mechanisms

## 🚨 Troubleshooting

### Common Issues

#### Agent Not Responding
```bash
# Check if the AI provider is accessible
curl -I https://api.openai.com/v1/models

# Verify API key is correct
npx tsx src/cli.ts config --show
```

#### Tool Execution Failures
```
🤖 > status
# Check the agent status and recent errors

🤖 > changes
# Review recent changes for issues
```

#### Configuration Issues
```bash
# Reset configuration
npx tsx src/cli.ts config --reset

# Reconfigure interactively
npx tsx src/cli.ts config
```

### Getting Help

1. Use the `help` command for quick reference
2. Check the `status` command for agent state
3. Review `changes` to see what the agent has done
4. Use `diff <id>` to see detailed changes

## 📚 Tips and Tricks

### Effective Prompting

- **Be Specific**: "Create a React component with TypeScript" vs "Create a component"
- **Provide Context**: "Fix the authentication bug in login.js" vs "Fix the bug"
- **Set Scope**: "Search for TODO comments in the src directory" vs "Find TODOs"

### Workflow Optimization

- **Start Small**: Begin with simple tasks to understand the agent's capabilities
- **Use Git**: Always work in a git repository for safety
- **Review Changes**: Regularly check `changes` to see what the agent has done
- **Iterative Approach**: Break complex tasks into smaller, manageable pieces

### Advanced Usage

- **Chain Commands**: The agent can handle complex multi-step tasks
- **Context Awareness**: The agent remembers previous conversation context
- **Tool Integration**: All tools work together seamlessly
- **Error Recovery**: The agent can detect and fix its own mistakes

---

For more information, see the main [README.md](README.md) file.
