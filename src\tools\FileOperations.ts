import { promises as fs } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { z } from 'zod';
import { nanoid } from 'nanoid';
import { Tool, ToolResult, AgentContext, ChangeRecord } from '../types';

// File Read Tool
const readFileSchema = z.object({
  path: z.string().describe('Path to the file to read'),
  encoding: z.enum(['utf8', 'binary', 'base64']).default('utf8').optional()
});

const readFileTool: Tool = {
  name: 'read_file',
  description: 'Read the contents of a file',
  schema: readFileSchema,
  category: 'file-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const fullPath = join(context.workingDirectory, params.path);
      const content = await fs.readFile(fullPath, params.encoding || 'utf8');
      
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          content,
          path: params.path,
          size: content.length,
          encoding: params.encoding || 'utf8'
        },
        timestamp: new Date(),
        metadata: {
          operation: 'read',
          filePath: params.path
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to read file: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// File Write Tool
const writeFileSchema = z.object({
  path: z.string().describe('Path to the file to write'),
  content: z.string().describe('Content to write to the file'),
  encoding: z.enum(['utf8', 'binary', 'base64']).default('utf8').optional(),
  createDirectories: z.boolean().default(true).optional()
});

const writeFileTool: Tool = {
  name: 'write_file',
  description: 'Write content to a file',
  schema: writeFileSchema,
  category: 'file-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const fullPath = join(context.workingDirectory, params.path);
      
      // Create directories if needed
      if (params.createDirectories) {
        await fs.mkdir(dirname(fullPath), { recursive: true });
      }

      // Read existing content for diff tracking
      let oldContent: string | undefined;
      try {
        oldContent = await fs.readFile(fullPath, 'utf8');
      } catch {
        // File doesn't exist, that's okay
      }

      // Write new content
      await fs.writeFile(fullPath, params.content, params.encoding || 'utf8');

      // Create change record
      const changeRecord: ChangeRecord = {
        id: nanoid(),
        type: oldContent ? 'file-modify' : 'file-create',
        path: params.path,
        timestamp: new Date(),
        diff: oldContent ? {
          oldContent,
          newContent: params.content,
          hunks: [], // Will be computed by diff tracker
          stats: {
            additions: 0,
            deletions: 0,
            changes: 0
          }
        } : undefined
      };

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          path: params.path,
          bytesWritten: params.content.length,
          created: !oldContent
        },
        timestamp: new Date(),
        metadata: {
          operation: oldContent ? 'modify' : 'create',
          filePath: params.path,
          fileChanges: [changeRecord]
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to write file: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// File Delete Tool
const deleteFileSchema = z.object({
  path: z.string().describe('Path to the file to delete')
});

const deleteFileTool: Tool = {
  name: 'delete_file',
  description: 'Delete a file',
  schema: deleteFileSchema,
  category: 'file-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const fullPath = join(context.workingDirectory, params.path);
      
      // Read content before deletion for change tracking
      let oldContent: string | undefined;
      try {
        oldContent = await fs.readFile(fullPath, 'utf8');
      } catch {
        // File doesn't exist
        throw new Error('File does not exist');
      }

      await fs.unlink(fullPath);

      // Create change record
      const changeRecord: ChangeRecord = {
        id: nanoid(),
        type: 'file-delete',
        path: params.path,
        timestamp: new Date(),
        diff: {
          oldContent,
          newContent: '',
          hunks: [],
          stats: {
            additions: 0,
            deletions: oldContent.split('\n').length,
            changes: 0
          }
        }
      };

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          path: params.path,
          deleted: true
        },
        timestamp: new Date(),
        metadata: {
          operation: 'delete',
          filePath: params.path,
          fileChanges: [changeRecord]
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to delete file: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// List Directory Tool
const listDirectorySchema = z.object({
  path: z.string().describe('Path to the directory to list'),
  recursive: z.boolean().default(false).optional(),
  includeHidden: z.boolean().default(false).optional()
});

const listDirectoryTool: Tool = {
  name: 'list_directory',
  description: 'List contents of a directory',
  schema: listDirectorySchema,
  category: 'file-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const fullPath = join(context.workingDirectory, params.path);
      const items = await fs.readdir(fullPath, { withFileTypes: true });

      const result = [];
      
      for (const item of items) {
        if (!params.includeHidden && item.name.startsWith('.')) {
          continue;
        }

        const itemPath = join(params.path, item.name);
        const fullItemPath = join(fullPath, item.name);
        
        let stats;
        try {
          stats = await fs.stat(fullItemPath);
        } catch {
          continue;
        }

        const fileInfo = {
          name: item.name,
          path: itemPath,
          type: item.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime
        };

        result.push(fileInfo);

        // Recursive listing
        if (params.recursive && item.isDirectory()) {
          try {
            const subResult = await this.execute({
              path: itemPath,
              recursive: true,
              includeHidden: params.includeHidden
            }, context);
            
            if (subResult.success && Array.isArray(subResult.output.items)) {
              result.push(...subResult.output.items);
            }
          } catch {
            // Skip directories we can't read
          }
        }
      }

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          path: params.path,
          items: result,
          count: result.length
        },
        timestamp: new Date(),
        metadata: {
          operation: 'list',
          directoryPath: params.path
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to list directory: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Search Files Tool
const searchFilesSchema = z.object({
  pattern: z.string().describe('Search pattern (regex)'),
  path: z.string().default('.').optional().describe('Directory to search in'),
  filePattern: z.string().optional().describe('File name pattern to match'),
  maxResults: z.number().default(100).optional(),
  caseSensitive: z.boolean().default(false).optional()
});

const searchFilesTool: Tool = {
  name: 'search_files',
  description: 'Search for text patterns in files',
  schema: searchFilesSchema,
  category: 'file-operations',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const searchPath = join(context.workingDirectory, params.path || '.');
      const results: any[] = [];
      const flags = params.caseSensitive ? 'g' : 'gi';
      const searchRegex = new RegExp(params.pattern, flags);
      const fileRegex = params.filePattern ? new RegExp(params.filePattern, 'i') : null;

      async function searchInDirectory(dirPath: string): Promise<void> {
        const items = await fs.readdir(dirPath, { withFileTypes: true });

        for (const item of items) {
          if (results.length >= params.maxResults!) break;

          const itemPath = join(dirPath, item.name);
          const relativePath = itemPath.replace(context.workingDirectory, '').replace(/^[\/\\]/, '');

          if (item.isDirectory() && !item.name.startsWith('.')) {
            await searchInDirectory(itemPath);
          } else if (item.isFile()) {
            // Check file name pattern
            if (fileRegex && !fileRegex.test(item.name)) {
              continue;
            }

            try {
              const content = await fs.readFile(itemPath, 'utf8');
              const lines = content.split('\n');
              
              for (let i = 0; i < lines.length; i++) {
                const matches = lines[i].match(searchRegex);
                if (matches) {
                  results.push({
                    file: relativePath,
                    line: i + 1,
                    content: lines[i].trim(),
                    matches: matches.length
                  });

                  if (results.length >= params.maxResults!) break;
                }
              }
            } catch {
              // Skip files we can't read
            }
          }
        }
      }

      await searchInDirectory(searchPath);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          pattern: params.pattern,
          results,
          totalMatches: results.length,
          searchPath: params.path || '.'
        },
        timestamp: new Date(),
        metadata: {
          operation: 'search',
          searchPattern: params.pattern
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to search files: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Export all tools as default array
export default [
  readFileTool,
  writeFileTool,
  deleteFileTool,
  listDirectoryTool,
  searchFilesTool
];
